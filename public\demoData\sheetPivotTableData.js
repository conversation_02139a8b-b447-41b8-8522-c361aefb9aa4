window.sheetPivotTableData = {
	"name": "PivotTableData",
	"color": "",
	"config": {
		"merge": {}
	},
	"index": "6",
	"chart": [],
	"status": 0,
	"order": "6",
	"hide": 0,
	"column": 18,
	"row": 36,
	"celldata": [{
		"r": 0,
		"c": 0,
		"v": {
			"m": "Mock test",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Mock test"
		}
	}, {
		"r": 0,
		"c": 1,
		"v": {
			"m": "student",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "student"
		}
	}, {
		"r": 0,
		"c": 2,
		"v": {
			"m": "class",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "class"
		}
	}, {
		"r": 0,
		"c": 3,
		"v": {
			"m": "subject",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "subject"
		}
	}, {
		"r": 0,
		"c": 4,
		"v": {
			"m": "score",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "score"
		}
	}, {
		"r": 1,
		"c": 0,
		"v": {
			"m": "first round",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "first round"
		}
	}, {
		"r": 1,
		"c": 1,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Joy",
			"m": "Joy"
		}
	}, {
		"r": 1,
		"c": 2,
		"v": {
			"m": "Class one",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Class one"
		}
	}, {
		"r": 1,
		"c": 3,
		"v": {
			"m": "English",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "English"
		}
	}, {
		"r": 1,
		"c": 4,
		"v": {
			"v": 96,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "96"
		}
	}, {
		"r": 2,
		"c": 0,
		"v": {
			"m": "first round",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "first round"
		}
	}, {
		"r": 2,
		"c": 1,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Joy",
			"m": "Joy"
		}
	}, {
		"r": 2,
		"c": 2,
		"v": {
			"m": "Class one",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Class one"
		}
	}, {
		"r": 2,
		"c": 3,
		"v": {
			"m": "mathematics",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "mathematics"
		}
	}, {
		"r": 2,
		"c": 4,
		"v": {
			"v": 110,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "110"
		}
	}, {
		"r": 3,
		"c": 0,
		"v": {
			"m": "first round",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "first round"
		}
	}, {
		"r": 3,
		"c": 1,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Joy",
			"m": "Joy"
		}
	}, {
		"r": 3,
		"c": 2,
		"v": {
			"m": "Class one",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Class one"
		}
	}, {
		"r": 3,
		"c": 3,
		"v": {
			"m": "foreign language",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "foreign language"
		}
	}, {
		"r": 3,
		"c": 4,
		"v": {
			"v": 87,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "87"
		}
	}, {
		"r": 4,
		"c": 0,
		"v": {
			"m": "first round",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "first round"
		}
	}, {
		"r": 4,
		"c": 1,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Joy",
			"m": "Joy"
		}
	}, {
		"r": 4,
		"c": 2,
		"v": {
			"m": "Class one",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Class one"
		}
	}, {
		"r": 4,
		"c": 3,
		"v": {
			"m": "science",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "science"
		}
	}, {
		"r": 4,
		"c": 4,
		"v": {
			"v": 266,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "266"
		}
	}, {
		"r": 5,
		"c": 0,
		"v": {
			"m": "first round",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "first round"
		}
	}, {
		"r": 5,
		"c": 1,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Tim",
			"m": "Tim"
		}
	}, {
		"r": 5,
		"c": 2,
		"v": {
			"m": "Class one",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Class one"
		}
	}, {
		"r": 5,
		"c": 3,
		"v": {
			"m": "English",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "English"
		}
	}, {
		"r": 5,
		"c": 4,
		"v": {
			"v": 92,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "92"
		}
	}, {
		"r": 6,
		"c": 0,
		"v": {
			"m": "first round",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "first round"
		}
	}, {
		"r": 6,
		"c": 1,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Tim",
			"m": "Tim"
		}
	}, {
		"r": 6,
		"c": 2,
		"v": {
			"m": "Class one",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Class one"
		}
	}, {
		"r": 6,
		"c": 3,
		"v": {
			"m": "mathematics",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "mathematics"
		}
	}, {
		"r": 6,
		"c": 4,
		"v": {
			"v": 100,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "100"
		}
	}, {
		"r": 7,
		"c": 0,
		"v": {
			"m": "first round",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "first round"
		}
	}, {
		"r": 7,
		"c": 1,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Tim",
			"m": "Tim"
		}
	}, {
		"r": 7,
		"c": 2,
		"v": {
			"m": "Class one",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Class one"
		}
	}, {
		"r": 7,
		"c": 3,
		"v": {
			"m": "foreign language",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "foreign language"
		}
	}, {
		"r": 7,
		"c": 4,
		"v": {
			"v": 90,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "90"
		}
	}, {
		"r": 8,
		"c": 0,
		"v": {
			"m": "first round",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "first round"
		}
	}, {
		"r": 8,
		"c": 1,
		"v": {
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Tim",
			"m": "Tim"
		}
	}, {
		"r": 8,
		"c": 2,
		"v": {
			"m": "Class one",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Class one"
		}
	}, {
		"r": 8,
		"c": 3,
		"v": {
			"m": "science",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "science"
		}
	}, {
		"r": 8,
		"c": 4,
		"v": {
			"v": 255,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "255"
		}
	}, {
		"r": 9,
		"c": 0,
		"v": {
			"m": "first round",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "first round"
		}
	}, {
		"r": 9,
		"c": 1,
		"v": {
			"m": "Alex",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Alex"
		}
	}, {
		"r": 9,
		"c": 2,
		"v": {
			"m": "Class one",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Class one"
		}
	}, {
		"r": 9,
		"c": 3,
		"v": {
			"m": "English",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "English"
		}
	}, {
		"r": 9,
		"c": 4,
		"v": {
			"v": 108,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "108"
		}
	}, {
		"r": 10,
		"c": 0,
		"v": {
			"m": "first round",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "first round"
		}
	}, {
		"r": 10,
		"c": 1,
		"v": {
			"m": "Alex",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Alex"
		}
	}, {
		"r": 10,
		"c": 2,
		"v": {
			"m": "Class one",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Class one"
		}
	}, {
		"r": 10,
		"c": 3,
		"v": {
			"m": "mathematics",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "mathematics"
		}
	}, {
		"r": 10,
		"c": 4,
		"v": {
			"v": 117,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "117"
		}
	}, {
		"r": 11,
		"c": 0,
		"v": {
			"m": "first round",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "first round"
		}
	}, {
		"r": 11,
		"c": 1,
		"v": {
			"m": "Alex",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Alex"
		}
	}, {
		"r": 11,
		"c": 2,
		"v": {
			"m": "Class one",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Class one"
		}
	}, {
		"r": 11,
		"c": 3,
		"v": {
			"m": "foreign language",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "foreign language"
		}
	}, {
		"r": 11,
		"c": 4,
		"v": {
			"v": 88,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "88"
		}
	}, {
		"r": 12,
		"c": 0,
		"v": {
			"m": "first round",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "first round"
		}
	}, {
		"r": 12,
		"c": 1,
		"v": {
			"m": "Alex",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Alex"
		}
	}, {
		"r": 12,
		"c": 2,
		"v": {
			"m": "Class one",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "Class one"
		}
	}, {
		"r": 12,
		"c": 3,
		"v": {
			"m": "science",
			"ct": {
				"fa": "General",
				"t": "g"
			},
			"v": "science"
		}
	}, {
		"r": 12,
		"c": 4,
		"v": {
			"v": 278,
			"ct": {
				"fa": "General",
				"t": "n"
			},
			"m": "278"
		}
	}],
	"ch_width": 4748,
	"rh_height": 1790,
	"luckysheet_select_save": [{
		"row": [0, 0],
		"column": [0, 0]
	}],
	"luckysheet_selection_range": [],
	"scrollLeft": 0,
	"scrollTop": 0
}

// export default sheetPivotTableData;