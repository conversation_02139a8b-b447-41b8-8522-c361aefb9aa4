import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  getPerformanceBottomRanking = '/business/performance/getPerformanceBottomRanking',
  getPerformanceKpiTrend = '/business/performance/getPerformanceKpiTrend',
  getPerformanceRadarData = '/business/performance/getPerformanceRadarData',
  getPerformanceRanking = '/business/performance/getPerformanceRanking',
}
// 获取绩效倒数 10 名数据
export async function getPerformanceBottomRanking(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getPerformanceBottomRanking,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
// 获取绩效 KPI 趋势数据（按时间维度）
export async function getPerformanceKpiTrend(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getPerformanceKpiTrend,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
// 获取绩效雷达图数据
export async function getPerformanceRadarData(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getPerformanceRadarData,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
// 获取绩效排名数据
export async function getPerformanceRanking(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getPerformanceRanking,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
