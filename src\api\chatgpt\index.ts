import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  Send = '/system/demo/test-chatgpt',
}

/**
 * @description: 发送
 */
export async function sendChatGpt(text: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<string>(
    {
      url: Api.Send,
      params: {
        text: text,
      },
      timeout: 30000,
    },
    {
      errorMessageMode: mode,
    },
  );
}
