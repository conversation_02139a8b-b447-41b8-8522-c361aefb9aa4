import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  employeePage = '/business/ShopAssistantManagement/page',
  employeeDel = '/business/ShopAssistantManagement',
}

/**
 * @description: 终端员工列表(分页)
 */
export async function employeePage(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.employeePage,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除员工
 */
export async function employeeDel(ids?: any[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete(
    {
      url: Api.employeeDel,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
