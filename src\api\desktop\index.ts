import { DesktopPageParams, FirstMarkParams } from './model';
import { ErrorMessageMode } from '/#/axios';
import { requestProps } from '/@/components/Form';
import { DesktopData } from '/@/model/desktop/designer';
import { defHttp } from '/@/utils/http/axios';

export const httpRequest = async (request: requestProps, options?: any) => {
  const url = '/magic-api/' + request.requestUrl;
  return defHttp.desktopApi(url, request.requestType, options);
};

enum Api {
  Page = '/desktop/schema/page',
  Desktop = '/desktop/schema',
  CopyDesktop = '/desktop/schema/copy',
  Info = '/desktop/schema/info',
  ExportDesktop = '/desktop/schema/export',
  HistoryList = '/desktop/history/page',
  MyTaskCount = '/workflow/execute/count',
  FIRST = '/desktop/schema/first',
  HistoryInfo = '/desktop/history/info',
}
/**
 * @description: 历史记录列表
 */
export async function getHistoryPageList(
  params: DesktopPageParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<any>(
    {
      url: Api.HistoryList,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询分页列表
 */
export async function getPageList(params: DesktopPageParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询角色授权可使用的桌面设计列表
 */
export async function getRolePageList(params: DesktopPageParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.Desktop + '/enabled-page',
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增桌面
 */
export async function addDesktop(info: DesktopData, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Desktop,
      params: info,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 编辑桌面
 */
export async function editDesktop(info: DesktopData, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.Desktop,
      params: info,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除
 */
export async function deleteDesktop(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.Desktop,
      data: [id],
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 复制桌面
 */
export async function copyDesktop(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.CopyDesktop,
      data: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取信息
 */
export async function getDesktopInfo(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<DesktopData>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 导出
 */
export async function exportDesktop(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.ExportDesktop,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
//
/**
 * @description: 我的任务合计
 */
export async function getMyTaskCount(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.MyTaskCount,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 编辑桌面设计是否首页
 */
export async function editFirstMark(info: FirstMarkParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.FIRST,
      params: info,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 启用禁用
 */
export async function setEnabled(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.Desktop + '/enabled',
      params: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 设为默认首页
 */
export async function setFirstHome(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.Desktop + '/set-default',
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取历史记录详情
 */
export async function getHistoryInfo(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<DesktopData>(
    {
      url: Api.HistoryInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
