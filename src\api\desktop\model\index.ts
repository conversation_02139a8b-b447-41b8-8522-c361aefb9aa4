import { BasicPageParams } from '../../model/baseModel';

export interface DesktopPageModel {
  id: string;

  code: string;

  name: string;

  backgroundUrl: string;

  isFirst: number; //是否首页
  enabledMark: number;
}

export interface FirstMarkParams {
  id: string;

  isFirst: number;
}

/**
 * @description: 分页 模型
 */
export type DesktopPageParams = BasicPageParams;

export interface DesktopAuthPage {
  id: string;
  backgroundUrl: string;
  name: string;

  checked: boolean;
}
