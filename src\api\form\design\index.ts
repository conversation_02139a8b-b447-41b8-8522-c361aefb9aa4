import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import {
  FormTemplateListModel,
  FormTemplateListParams,
  FormTemplateListResultModel,
  FormTemplateModel,
  versionModal,
} from './model';
import { QueryConfig } from '/@/model/generator/listConfig';

enum Api {
  FormTemplate = '/form/template',
  List = '/form/template/list',
  Page = '/form/template/page',
  Enabled = '/form/template/enabled-page',
  Info = '/form/template/info',
  Status = '/form/template/status',
  DataFirst = '/form/template/data-first',
  CodeFirst = '/form/template/code-first',
  History = '/form/history/list',
  HistoryInfo = '/form/history/info',
  HistoryVersion = '/form/history/set-version',
  FormComplexAdd = '/desktop/schema/complex-add',
  FormComplexList = '/form/template/desk-form-page',
  ReleaseInfo = '/desktop/schema/get-release-info',
  TableInfo = '/desktop/schema/get-table-info',
  ComplexDelete = '/desktop/schema/complex-delete',
  ComplexEdit = '/desktop/schema/complex-update',
  ComplexInfo = '/desktop/schema/complex-info',
  ComplexQuery = '/desktop/schema/complex-query',
  FormAdd = '/form/execute/form/add',
  Export = '/form/template/export',
}

/**
 * @description: 查询所有模板 （不分页）
 */
export async function getFormTemplateList(
  params?: FormTemplateListParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<FormTemplateListModel[]>(
    {
      url: Api.List,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description:  查询所有模板 （分页）
 */
export async function getFormTemplatePage(params: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FormTemplateListResultModel>(
    {
      url: Api.Enabled,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description:  查询所有启用的模板 （分页）
 */
export async function getFormTemplateEnabledPage(
  params: Recordable,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<FormTemplateListResultModel>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除模板（批量删除）
 */
export async function deleteFormTemplate(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.FormTemplate,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取模板详情信息
 */
export async function getFormTemplate(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FormTemplateModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新模板状态
 */
export async function updateFormTemplateStatus(
  id: string,
  status: number,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<number>(
    {
      url: Api.Status,
      data: {
        id,
        enabledMark: status,
      },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新模板（数据优先）
 */
export async function updateDataFirstFormTemplate(
  formTemplate: Recordable,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<number>(
    {
      url: Api.DataFirst,
      data: formTemplate,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增模板（数据优先）
 */
export async function addDataFirstFormTemplate(
  formTemplate: Recordable,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<number>(
    {
      url: Api.DataFirst,
      data: formTemplate,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询自定义表单历史记录
 */
export async function getFormHistory(formId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.History,
      params: { formId },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询自定义表单历史记录预览
 */
export async function getFormHistoryInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FormTemplateModel>(
    {
      url: Api.HistoryInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description:  自定义表单历史记录更新版本
 */
export async function setFormHistoryVersion(data: versionModal, mode: ErrorMessageMode = 'modal') {
  return defHttp.put(
    {
      url: Api.HistoryVersion,
      data: data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增模板（界面优先、简易模板）
 */
export async function addCodeFirstFormTemplate(
  formTemplate: Recordable,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<number>(
    {
      url: Api.CodeFirst,
      data: formTemplate,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新模板（界面优先、简易模板）
 */
export async function updateCodeFirstFormTemplate(
  formTemplate: Recordable,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<number>(
    {
      url: Api.CodeFirst,
      data: formTemplate,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 桌面设计 复杂列表 表单新增
 */
export async function addComplexFormExecute(formId, formData, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.FormComplexAdd,
      data: { formId, formData },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 桌面设计 复杂列表 表单编辑
 */
export async function editComplexFormExecute(
  data: { formId: string; formData: any; pkName: string },
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<number>(
    {
      url: Api.ComplexEdit,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询复杂表单组件 表单列表（自定义表单，代码生成器）
 */
export async function getFormComplexList(params: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FormTemplateListResultModel>(
    {
      url: Api.FormComplexList,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 桌面设计-复杂列表页-根据表单id查询发布信息
 */
export async function getReleaseInfo(formId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FormTemplateListResultModel>(
    {
      url: Api.ReleaseInfo,
      params: { formId },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取表单所有字段信息
 */
export async function getTableInfo(formId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<{ pkName: string; deskColumnsVoList: Array<{ prop: string; label: string }> }>(
    {
      url: Api.TableInfo,
      params: { formId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取复杂列表详情
 */
export async function getComplexInfo(
  data: {
    formId: string;
    pkName: string;
    pkValue: string;
  },
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<boolean>(
    {
      url: Api.ComplexInfo,
      data: data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 桌面设计-复杂列表页-删除(公共接口)
 */
export async function deleteComplex(
  params: {
    formId: string;
    pkName: string;
    pkValue: string;
  },
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.delete<boolean>(
    {
      url: Api.ComplexDelete,
      data: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 桌面设计-复杂列表页-根据发布id获取查询配置数据
 */
export async function getComplexQuery(formId, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<QueryConfig[]>(
    {
      url: Api.ComplexQuery,
      data: { formId },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 自定义表单新增数据（推单使用）
 */
export async function addFormInfo(data: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.FormAdd,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 导出表单设计
 */
export async function exportFormDesign(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Export,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
