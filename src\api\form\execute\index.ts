import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { FETCH_SETTING } from '/@/components/Table/src/const';

enum Api {
  FormExecute = '/form/execute',
  List = '/form/execute/list',
  Page = '/form/execute/page',
  Info = '/form/execute/info',
  WorkflowInfo = '/form/execute/workflow/info',
  Add = '/form/execute/add',
  Update = '/form/execute/update',
  Delete = '/form/execute/delete',
  Export = '/form/execute/export',
  DataAuth = '/form/execute/data-auth',
  ComplexPage = '/desktop/schema/complex-page',
  SubData = '/form/execute/sub-data',
}

/**
 * @description: 查询所有模板 （不分页）
 */
export async function getFormExecuteList(data: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<Recordable[]>(
    {
      url: Api.List,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description:  查询所有模板 （分页）
 */
export async function getFormExecutePage(data: Recordable, mode: ErrorMessageMode = 'modal') {
  const { headers, ...paramsData } = data;
  return defHttp.post<Recordable>(
    {
      url: Api.Page,
      data: {
        [FETCH_SETTING.pageField]: paramsData.params[FETCH_SETTING.pageField],
        [FETCH_SETTING.sizeField]: paramsData.params[FETCH_SETTING.sizeField],
        ...paramsData,
      },
      headers,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除自定义表单数据（批量删除）
 */
export async function deleteFormExecute(data: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Delete,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 自定义表单获取详情
 */
export async function getFormExecute(data: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<Recordable>(
    {
      url: Api.Info,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 工作流表单使用自定义表单获取详情
 */
export async function getFormExecuteWorkflow(data: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<Recordable>(
    {
      url: Api.WorkflowInfo,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 表单执行新增
 */
export async function addFormExecute(data: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Add,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 表单执行修改
 */
export async function updateFormExecute(data: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Update,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 导出
 */
export async function exportForm(params: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.Export,
      method: 'POST',
      params,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改数据权限
 */
export async function setDataAuth(
  params: Recordable,
  data: Recordable,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<boolean>(
    {
      url: Api.DataAuth,
      params,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description:  桌面设计-复杂列表页-获取列表数据 分页
 */
export async function getComplexPage(data: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<Recordable>(
    {
      url: Api.ComplexPage,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 自定义表单查询子表数据
 */
export async function getSubData(params?, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.SubData,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
