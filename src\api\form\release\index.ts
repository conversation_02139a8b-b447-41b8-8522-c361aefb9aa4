import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import {
  FormReleaseListModel,
  FormReleaseListParams,
  FormReleaseListResultModel,
  FormReleaseModel,
} from './model';

enum Api {
  FormRelease = '/form/release',
  List = '/form/release/list',
  Page = '/form/release/page',
  Info = '/form/release/info',
  FormMenu = '/form/release/menu-together-delete',
}

/**
 * @description: 查询所有模板 （不分页）
 */
export async function getFormReleaseList(
  params?: FormReleaseListParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<FormReleaseListModel[]>(
    {
      url: Api.FormRelease,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description:  查询所有模板 （分页）
 */
export async function getFormReleasePage(params: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FormReleaseListResultModel>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除模板（批量删除）
 */
export async function deleteFormRelease(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.FormRelease,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除模板同时删除菜单
 */
export async function deleteFormAndMenu(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.FormMenu,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取模板详情信息
 */
export async function getFormRelease(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FormReleaseModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改
 */
export async function updateFormRelease(release: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.FormRelease,
      data: release,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增
 */
export async function addFormRelease(release: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.FormRelease,
      data: release,
    },
    {
      errorMessageMode: mode,
    },
  );
}
