import { BasicFetchResult, BasicPageParams } from '/@/api/model/baseModel';

export interface FormReleaseListParams {
  keyword?: string;
}

/**
 * @description: Request list interface parameters
 */
export type FormReleasePageParamsModel = BasicPageParams & FormReleaseListParams;

export interface FormReleaseListModel {
  id: string; //主键
  formId: string; //表单模板id
  menuId: string; //菜单id
  // configJson: FormReleaseConfig; //表单发布
  sortCode: number; //排序号
  remark: string; //备注
}

export interface FormReleaseModel {
  id: string; //主键
  formId: string; //表单模板id
  menuId: string; //菜单id
  configJson: string; //表单发布配置
  sortCode: number; //排序号
  remark: string; //备注
}

/**
 * @description: Request list return value
 */
export type FormReleaseListResultModel = BasicFetchResult<FormReleaseListModel>;
