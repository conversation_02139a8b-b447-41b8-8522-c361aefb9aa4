import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { FormViewInfoData, FormViewListResultModel } from './model';

enum Api {
  UserView = '/form/view/get-user-view',
  FormView = '/form/view',
  FormViewInfo = '/form/view/info',
}
/**
 * @description: 修改
 */
export async function updateFormView(release: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.FormView,
      data: release,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增
 */
export async function addFormView(release: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.FormView,
      data: release,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 删除
 */
export async function deleteFormView(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.FormView,
      data: [id],
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取信息
 */
export async function getFormViewInfo(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FormViewInfoData>(
    {
      url: Api.FormViewInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description:  查询用户下所有视图
 */
export async function getUserViewFormView(objectId: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FormViewListResultModel>(
    {
      url: Api.UserView,
      params: { objectId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
