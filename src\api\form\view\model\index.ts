import { IS_PUBLIC, QUERY_TYPE } from '/@/enums/formViewEnum';
import { FormType } from '/@/enums/workflowEnum';

export interface FormViewInfoData {
  id: string; //主键
  code: String;
  configJson: String;
  formViewType: FormType;
  isPublic: IS_PUBLIC;
  name: String;
  objectId: String;
  queryType: QUERY_TYPE;
  combinationFormula?: String;
}
export interface FormViewListModel {
  id: string; //主键
  formId: string; //表单模板id
  menuId: string; //菜单id
  // configJson: FormReleaseConfig; //表单发布
  sortCode: number; //排序号
  remark: string; //备注
}

export type FormViewListResultModel = Array<FormViewInfoData>;
