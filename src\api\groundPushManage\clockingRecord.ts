import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  adminPage = '/business/sign/adminPage',
  updateSet = '/business/punchset/update',
  visitMattersList = '/business/punchset/visitMattersList',
  updateVisitMatters = '/business/punchset/updateVisitMatters',
  getSet = '/business/punchset/info',
}

/**
 * @description: 打卡记录列表(分页)
 */
export async function getAdminPage(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.adminPage,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 签到设置
 */
export async function updateSet(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.updateSet,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 到点事项设置
 */
export async function updateVisitMattersList(data?: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.visitMattersList,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 到点事项设置编辑
 */
export async function updateVisitMatters(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.updateVisitMatters,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询签到设置
 */
export async function getSet(params?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getSet,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
