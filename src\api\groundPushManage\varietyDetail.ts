import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  pageProductGroupProvince = '/business/Product/pageProductGroupProvince',
  deleteProductGroupProvince = '/business/Product/deleteProductGroupProvince',
  getProductGroupProvince = '/business/Product/getProductGroupProvince',
  importProductGroupProvince = '/business/Product/importProductGroupProvince',
  saveProductGroupProvince = '/business/Product/saveProductGroupProvince',
  pageProductGroupType = '/business/Product/pageProductGroup',
  getProductALL = '/business/Product/getProductALL'
}
export async function getPageProductGroupProvince(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.pageProductGroupProvince,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function deleteProductGroupProvince(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.deleteProductGroupProvince,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function getProductGroupProvince(params?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getProductGroupProvince,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function importProductGroupProvince(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.importProductGroupProvince,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function saveProductGroupProvince(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.saveProductGroupProvince,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function pageProductGroupType(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.pageProductGroupType,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function getProductALL(data?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.getProductALL,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
