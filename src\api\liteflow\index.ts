import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { LiteflowListModel } from './model';

enum Api {
  List = '/liteflow/chain/list',
  Execute = '/liteflow/chain/execute',
  Page = '/liteflow/chain/page',
  Delete = '/liteflow/chain',
}

/**
 * @description: 查询所有规则文件列表（不分页）
 */
export async function getLiteflowList(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<LiteflowListModel[]>(
    {
      url: Api.List,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询所有规则文件列表（分页）
 */
export async function getLiteflowPage(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 发送
 */
export async function execute(id: string, param: object, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Execute,
      params: {
        id,
        param,
      },
      timeout: 30000,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除
 */
export async function deleteLiteflow(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Delete,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 新增
 */
export async function addLiteflow(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.Delete,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 新增
 */
export async function updateLiteflow(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.put(
    {
      url: Api.Delete,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
