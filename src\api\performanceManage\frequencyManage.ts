import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  getFrequency = '/business/taskmanagement/getFrequency',
  getVisit = '/business/taskDetail/visitList',
}

export async function frequencyFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getFrequency,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function visitFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.getVisit,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
