import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  contractPage = '/business/contract/page',
  contractInfo = '/business/contract/info',
  contractAdd = '/business/contract/add',
  contractUpdate = '/business/contract/update',
  contractPushRetry = '/business/contract/contractPushRetry',
  submitApproval = '/business/contract/submitApproval',
  signNodelist = '/business/contractSignNode/signNodelist',
  getOtcContractEnterpriseList = '/business/contractEnterprise/getOtcContractEnterpriseList',
  generateSerialNumber = '/business/contract/generateSerialNumber',
  getContractChainList = '/business/account/getContractChainList',
  getApproveUrl = '/business/contract/getApproveUrl',
}

export async function getContractPage(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.contractPage,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
// 根据id查询otcContract信息
export async function contractInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.contractInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
// 新增
export async function addContract(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.contractAdd,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
// 编辑
export async function contractUpdate(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.contractUpdate,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
// 重新推送协议
export async function contractPushRetry(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.contractPushRetry,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
// 推送协议内容到OA去审批
export async function submitApproval(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.submitApproval,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
// 协议签署信息表格
export async function signNodelist(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.signNodelist,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
// 甲方客户列表
export async function getOtcContractEnterpriseList(params = {}, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getOtcContractEnterpriseList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
// 获取code编号
export async function generateSerialNumber(params = {}, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.generateSerialNumber,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
// 获取ka连锁名称
export async function getContractChainList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.getContractChainList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
// 获取流程节点信息
export async function getApproveUrl(params = {}, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.getApproveUrl,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
