import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  fieldList = '/business/contractFiled/page',
  updateList = '/business/contractFiled/update',
  addList = '/business/contractFiled/add',
  deleteList = '/business/contractFiled/delete',
}

export async function getFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.fieldList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function updateFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.updateList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
export async function addFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.addList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}

export async function deleteFieldList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: Api.deleteList,
      params,
    },

    {
      errorMessageMode: mode,
    },
  );
}
