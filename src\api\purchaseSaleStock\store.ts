import {defHttp} from '/@/utils/http/axios';

import {ErrorMessageMode} from '/#/axios';

enum Api {
    productInventoryPage = '/business/inventorycustomer/productInventoryPage',
    productCount = '/business/inventorycustomer/getProductStaticCount',
    headInventoryPage = '/business/inventorycustomer/headInventoryPage',
    getHeadStaticCount = '/business/inventorycustomer/getHeadStaticCount',
    partInventoryPage = '/business/inventorycustomer/partInventoryPage',
    getPartStaticCount = '/business/inventorycustomer/getPartStaticCount',
    pageProductGroup = '/business/Product/pageProductGroup',
    pageProduct = '/business/Product/pageProduct',
    areaList = '/system/area/child',
    headInventoryAnalysePage = '/business/inventorycustomer/headInventoryAnalysePage',
    partInventoryAnalysePage = '/business/inventorycustomer/partInventoryAnalysePage',
    export = '/business/inventorycustomer/export'
}

/**
 * @description: 产品(分页)
 */
export async function getProductInventoryPage(data?: object, mode: ErrorMessageMode = 'modal') {
    return defHttp.post(
        {
            url: Api.productInventoryPage,
            data,
        },
        {
            errorMessageMode: mode,
        },
    );
}

/**
 * @description: 产品数量(分页)
 */
export async function getProductCount(data?: object, mode: ErrorMessageMode = 'modal') {
    return defHttp.post(
        {
            url: Api.productCount,
            data,
        },
        {
            errorMessageMode: mode,
        },
    );
}

/**
 * @description: 总部列表(分页)
 */
export async function getHeadInventoryPage(data?: object, mode: ErrorMessageMode = 'modal') {
    return defHttp.post(
        {
            url: Api.headInventoryPage,
            data,
        },
        {
            errorMessageMode: mode,
        },
    );
}

/**
 * @description: 总部列表(分页)
 */
export async function getHeadStaticCount(data?: object, mode: ErrorMessageMode = 'modal') {
    return defHttp.post(
        {
            url: Api.getHeadStaticCount,
            data,
        },
        {
            errorMessageMode: mode,
        },
    );
}


/**
 * @description: 分部列表(分页)
 */
export async function getPartInventoryPage(data?: object, mode: ErrorMessageMode = 'modal') {
    return defHttp.post(
        {
            url: Api.partInventoryPage,
            data,
        },
        {
            errorMessageMode: mode,
        },
    );
}

/**
 * @description: 分部列表(分页)
 */
export async function getPartStaticCount(data?: object, mode: ErrorMessageMode = 'modal') {
    return defHttp.post(
        {
            url: Api.getPartStaticCount,
            data,
        },
        {
            errorMessageMode: mode,
        },
    );
}

/**
 * @description: 品种
 */
export async function getProductGroup(data?: object, mode: ErrorMessageMode = 'modal') {
    return defHttp.post(
        {
            url: Api.pageProductGroup,
            data,
        },
        {
            errorMessageMode: mode,
        },
    );
}

/**
 * @description: 品规
 */
export async function getPageProduct(data?: object, mode: ErrorMessageMode = 'modal') {
    return defHttp.post(
        {
            url: Api.pageProduct,
            data,
        },
        {
            errorMessageMode: mode,
        },
    );
}


/**
 * @description: 省
 */
export async function areaList(params?: object, mode: ErrorMessageMode = 'modal') {
    return defHttp.get(
        {
            url: Api.areaList,
            params,
        },
        {
            errorMessageMode: mode,
        },
    );
}
/**
 * @description: 总部分析列表(分页)
 */
export async function headInventoryAnalysePage(data?: object, mode: ErrorMessageMode = 'modal') {
    return defHttp.post(
        {
            url: Api.headInventoryAnalysePage,
            data,
        },
        {
            errorMessageMode: mode,
        },
    );
}
/**
 * @description: 分部分析列表(分页)
 */
export async function partInventoryAnalysePage(data?: object, mode: ErrorMessageMode = 'modal') {
    return defHttp.post(
        {
            url: Api.partInventoryAnalysePage,
            data,
        },
        {
            errorMessageMode: mode,
        },
    );
}
/**
 * @description: 导出
 */
export async function exportData(data?: object, mode: ErrorMessageMode = 'modal') {
    return defHttp.download(
        {
            url: Api.export,
            method: 'POST',
            data,
            responseType: 'blob',
        },
        {
            errorMessageMode: mode,
        },
    );
}
