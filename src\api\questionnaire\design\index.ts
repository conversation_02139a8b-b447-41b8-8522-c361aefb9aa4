import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { QsTemplateListModel, QsTemplatePageParamsModel, QsTemplateInfoModel } from './model';
import { QuestionnaireConfig } from '/@/model/generator/generatorConfig';

enum Api {
  Questionnaire = '/system/questionnaire',
  Page = '/system/questionnaire/page',
  Info = '/system/questionnaire/info',
}

/**
 * @description: 查询问卷列表
 */
export async function getQuestionnaireList(
  params?: QsTemplatePageParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<QsTemplateListModel[]>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除问卷（批量删除）
 */
export async function deleteQuestionnaire(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Questionnaire,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取问卷详情信息
 */
export async function getQuestionnaireInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<QsTemplateInfoModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改问卷
 */
export async function updateQuestionnaire(
  QsTemplate: QuestionnaireConfig,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<number>(
    {
      url: Api.Questionnaire,
      data: QsTemplate,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增问卷
 */
export async function addQuestionnaire(
  QsTemplate: QuestionnaireConfig,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<number>(
    {
      url: Api.Questionnaire,
      data: QsTemplate,
    },
    {
      errorMessageMode: mode,
    },
  );
}
