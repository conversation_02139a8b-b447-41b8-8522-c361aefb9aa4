import { BasicPageParams } from '/@/api/model/baseModel';

export interface QsTemplateListParams {
  keyword?: string;
  startTime?: string;
  endTime?: string;
}

/**
 * @description: Request list interface parameters
 */
export type QsTemplatePageParamsModel = BasicPageParams & QsTemplateListParams;

export interface QsTemplateListModel {
  id: string; //主键
  name: string; //名称
  categoryName: string; //表单分类 关联 数据字典
  createUserName: string; //创建人
  createDate: string; //创建时间
  remark: string; //备注
}

export interface QsTemplateInfoModel {
  id: string; //主键
  name: string; //名称
  category: string; //表单分类 关联 数据字典
  content: string;
  remark: string; //备注
}
export interface versionModal {
  formId: string;
  id: string;
}
