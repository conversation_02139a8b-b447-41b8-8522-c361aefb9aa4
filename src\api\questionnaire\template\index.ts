import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  Execute = '/system/questionnaire/execute',
  Page = '/system/questionnaire/execute/page',
  Info = '/system/questionnaire/execute/info',
}

/**
 * @description: 查询生成问卷列表
 */
export async function getQnExecuteList(params?: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<Recordable>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除生成问卷
 */
export async function deleteQnExecute(data: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Execute,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取生成问卷详情信息
 */
export async function getQnExecuteInfo(params: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<Recordable>(
    {
      url: Api.Info,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增生成问卷
 */
export async function addQnExecute(QsTemplate: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Execute,
      data: QsTemplate,
    },
    {
      errorMessageMode: mode,
    },
  );
}
