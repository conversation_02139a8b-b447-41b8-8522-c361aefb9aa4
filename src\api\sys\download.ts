import { downloadParams } from './model/downloadModel';
import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';

/**
 * @description: 导出
 */
export async function downloadFile(
  downloadUrl: string,
  params?: downloadParams,
  method?: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.download(
    {
      url: downloadUrl,
      method: method || 'GET',
      params,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}
