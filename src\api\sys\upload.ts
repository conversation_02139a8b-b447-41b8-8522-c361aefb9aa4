import { UploadApiResult } from './model/uploadModel';
import { defHttp } from '/@/utils/http/axios';
import { UploadFileParams } from '/#/axios';
import { useGlobSetting } from '/@/hooks/setting';
const { uploadUrl = '' } = useGlobSetting();

/**
 * @description: Upload interface
 */
export function uploadApi(params: UploadFileParams) {
  return defHttp.uploadFile<UploadApiResult>(
    {
      url: uploadUrl,
    },
    params,
  );
}

/**
 * @description: Upload interface
 */
export function uploadMultiApi(params: UploadFileParams, folderid) {
  return defHttp.uploadFile<UploadApiResult>(
    {
      url: '/system/oss/multi-upload?folderId=' + folderid,
    },
    params,
  );
}

export const uploadSrc = '/system/oss/upload';

// 上传二进制文件生成图片
export async function uploadBlobApi(blob, filename) {
  try {
    const res = await upload<PERSON>pi({ name: 'file', file: blob, filename });
    if (res && res.fileUrl) {
      return res.fileUrl;
    } else {
      return '';
    }
  } catch (error) {
    return '';
  }
}
