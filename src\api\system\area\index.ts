import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { AreaListModel, AreaListParams, AreaModel } from './model';

enum Api {
  Province = '/system/area/province',
  Child = '/system/area/child',
  Info = '/system/area/info',
  Area = '/system/area',
  InfoMulti = '/system/area/info/multi',
}

/**
 * @description: 查询所有省份列表
 */
export async function getAreaProvinceList(
  params?: AreaListParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<AreaListModel>(
    {
      url: Api.Province,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询所有省份列表
 */
export async function getAreaList(params: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<AreaListModel[]>(
    {
      url: Api.Child,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除区域（批量删除）
 */
export async function deleteArea(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Area,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增区域
 */
export async function addArea(area: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Area,
      data: area,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取区域详情信息
 */
export async function getArea(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<AreaModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取区域详情信息（多个）
 */
export async function getAreaMulti(ids: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<AreaModel[]>(
    {
      url: Api.InfoMulti,
      params: { ids },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新岗位
 */
export async function updateArea(area: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.Area,
      data: area,
    },
    {
      errorMessageMode: mode,
    },
  );
}
