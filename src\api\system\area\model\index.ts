import { BasicFetchResult, BasicPageParams } from '/@/api/model/baseModel';

export interface AreaListParams {
  name?: string;
}

/**
 * @description: Request list interface parameters
 */
export type AreaPageListParamsModel = BasicPageParams & AreaListParams;

export interface AreaListModel {
  id: string; //主键
  parentId: string; //父级主键
  code: string; //区域编码
  name: string; //区域名称
  quickQuery: string; //快速查询
  simpleSpelling: string; //简拼
  layer: number; //层级
  sortCode: number; //排序号
  remark: string; //备注
}

export interface AreaModel {
  id: string; //主键
  parentId: string; //父级主键
  code: string; //区域编码
  name: string; //区域名称
  quickQuery: string; //快速查询
  simpleSpelling: string; //简拼
  layer: number; //层级
  sortCode: number; //排序号
  remark: string; //备注
}

/**
 * @description: Request list return value
 */
export type AreaListResultModel = BasicFetchResult<AreaListModel>;
