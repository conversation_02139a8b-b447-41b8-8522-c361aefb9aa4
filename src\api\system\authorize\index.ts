import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { AuthModel, AuthPageListResultModel, AuthPageListSearchModel } from './model';

enum Api {
  Page = '/authority/data-auth/page',
  List = '/authority/data-auth/list',
  Info = '/authority/data-auth/info',
  dataAuth = '/authority/data-auth',
  objectInfo = '/authority/data-auth/auth-objects',
}

/**
 * @description: 查询数据权限分页列表
 */
export async function getAuthPageList(
  params: AuthPageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<AuthPageListResultModel>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询数据权限列表
 */
export async function getAuthList(
  params: AuthPageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<AuthPageListResultModel>(
    {
      url: Api.List,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除数据权限（批量删除）
 */
export async function deleteAuth(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.dataAuth,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增数据权限
 */
export async function addAuth(auth: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.dataAuth,
      params: auth,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取数据权限信息
 */
export async function getAuth(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<AuthModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新数据权限
 */
export async function updateAuth(auth: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.dataAuth,
      data: auth,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取数据权限授权对象
 */
export async function getAuthObject(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<AuthModel>(
    {
      url: Api.objectInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
