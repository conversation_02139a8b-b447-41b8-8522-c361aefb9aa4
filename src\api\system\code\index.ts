import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { CodeRuleListParams, CodeListParams, CodeListModel, CodeRuleParams } from './model';

enum Api {
  List = '/system/code-rule/list',
  ListPage = '/system/code-rule/page',
  CodeRule = '/system/code-rule',
  Info = '/system/code-rule/info',
  Generate = '/system/code-rule/generate',
}

/**
 * @description: 获取编码规则（不分页）
 */
export async function getCodeRule(params?: CodeRuleListParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<CodeListModel>(
    {
      url: Api.List,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取编码规则（分页）
 */
export async function getCodeList(params?: CodeListParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<CodeListModel>(
    {
      url: Api.ListPage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除编码规则（批量删除）
 */
export async function deleteCodeRule(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.CodeRule,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增编码规则
 */
export async function addCodeRule(params: CodeRuleParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.CodeRule,
      data: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 编辑编码规则
 */
export async function editCodeRule(params: CodeRuleParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.CodeRule,
      data: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取编码规则详情信息
 */
export async function getCodeRuleInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 生成自动编码
 */
export async function generateCodeRule(encode: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<string>(
    {
      url: Api.Generate,
      params: { encode },
    },
    {
      errorMessageMode: mode,
    },
  );
}
