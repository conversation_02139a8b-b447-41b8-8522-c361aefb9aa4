import {
  DatabaseLinkModel,
  DatabaseLinkPageParamsModel,
  DatabaseLinkPageResultModel,
  DatabaseLinkParams,
  DatabaseLinkTableInfo,
} from './model';
import { ErrorMessageMode } from '/#/axios';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  DatabaseLink = '/system/databaselink',
  Test = '/system/databaselink/test',
  Page = '/system/databaselink/page',
  Info = '/system/databaselink/info',
  Table = '/system/databaselink/table',
  ColumnsName = '/system/databaselink/table/columns-name',
  MultiColumnsName = '/system/databaselink/table/columns-name/multi',
  Columns = '/system/databaselink/table/columns',
  MultiColumns = '/system/databaselink/table/columns/multi',
  Data = '/system/databaselink/table/data',
  Export = '/system/databaselink/export',
}

/**
 * @description: 查询数据库列表
 */
export async function getDatabaselinkTree(
  params?: DatabaseLinkParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DatabaseLinkModel[]>(
    {
      url: Api.DatabaseLink,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询数据库列表(分页)
 */
export async function getDatabaselinkPage(
  params?: DatabaseLinkPageParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DatabaseLinkPageResultModel>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询当前数据库连接所有表信息
 */
export async function getDatabaselinkTable(params?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<DatabaseLinkTableInfo[]>(
    {
      url: Api.Table,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询当前表数据库某个表的所有列名
 */
export async function getDatabaselinkTableColumnName(
  params?: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<string[]>(
    {
      url: Api.ColumnsName,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询当前表数据库多个表的所有列名
 */
export async function getDatabaselinkMultiTableColumnName(
  params?: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<Recordable>(
    {
      url: Api.MultiColumnsName,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询当前表数据库某个表的所有列信息
 */
export async function getDatabaselinkTableColumn(params?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any[]>(
    {
      url: Api.Columns,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询当前表数据库多个表的所有列信息
 */
export async function getDatabaselinkMultiTableColumns(
  params?: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<Recordable>(
    {
      url: Api.MultiColumns,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除链接（批量删除）
 */
export async function deleteDatabaseLink(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.DatabaseLink,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增连接
 */
export async function addDatabaseLink(databaseLink: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.DatabaseLink,
      data: databaseLink,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 测试链接
 */
export async function testDatabaseLink(databaseLink: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Test,
      data: databaseLink,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取部门信息
 */
export async function getDatabaseLink(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<DatabaseLinkModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新数据库连接
 */
export async function updateDatabaseLink(
  databaseLink: Recordable,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<number>(
    {
      url: Api.DatabaseLink,
      data: databaseLink,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询当前表数据库某个表的所有数据
 */
export async function getDatabaselinkTableData(params?: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any[]>(
    {
      url: Api.Data,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 导出
 */
export async function exportInfo(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.Export,
      method: 'POST',
      params: ids,
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 下载模板
 */
export async function downloadTemplate(mode: ErrorMessageMode = 'modal') {
  return defHttp.download(
    {
      url: Api.Export,
      method: 'GET',
      responseType: 'blob',
    },
    {
      errorMessageMode: mode,
    },
  );
}
