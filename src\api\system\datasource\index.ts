import {
  DatasourceModel,
  DatasourcePageParamsModel,
  DatasourcePageResultModel,
  DatasourceByIdParamsModel,
  DatasourceParams,
} from './model';
import { ErrorMessageMode } from '/#/axios';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  DataSource = '/system/datasource',
  List = '/system/datasource/list',
  Page = '/system/datasource/page',
  Info = '/system/datasource/info',
  DataSourceData = '/system/datasource/data',
  DataSourcePage = '/system/datasource/data/page',
  Column = '/system/datasource/column',
}

/**
 * @description: 根据数据源列表 （不分页）
 */
export async function getDatasourceList(
  params?: DatasourceParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DatasourceModel[]>(
    {
      url: Api.List,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 根据数据源列表 （分页）
 */
export async function getDatasourcePage(
  params: DatasourcePageParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DatasourcePageResultModel>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 根据数据源Id获取数据源详情
 */
export async function getDatasourceInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<DatasourceModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增数据源
 */
export async function addDatasource(datasource: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.DataSource,
      data: datasource,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新数据源
 */
export async function updateDatasource(datasource: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.DataSource,
      data: datasource,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除数据源（批量删除）
 */
export async function deleteDatasource(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.DataSource,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取外部接口
 */
export async function getApiData(api: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any[]>(
    {
      url: api,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 根据数据源Id获取数据（不分页）
 */
export async function getDatasourceData(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any[]>(
    {
      url: Api.DataSourceData,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 根据数据源Id获取数据（分页）
 */
export async function getDatasourceById(
  params: DatasourceByIdParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<any[]>(
    {
      url: Api.DataSourcePage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 根据数据源Id获取所有字段
 */
export async function getDatasourceColumn(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<string[]>(
    {
      url: Api.Column,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
