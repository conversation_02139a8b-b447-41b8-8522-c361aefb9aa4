import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import {
  DepartmentModel,
  DepartmentPageListParamsModel,
  DepartmentPageListResultModel,
  DepartmentTreeModel,
  DepartmentTreeParams,
  ApprovalModel,
  ApprovalUserModel,
  ApprovalUserParams,
} from './model';

enum Api {
  Page = '/organization/department/page',
  Tree = '/organization/department/tree',
  EnabledTree = '/organization/department/enabled-tree',
  Info = '/organization/department/info',
  MultiInfo = '/organization/department/info/multi',
  Department = '/organization/department',
  User = '/organization/department/dept-user-info',
  UpdateUser = '/organization/department/add-dept-user',
  Approval = '/organization/approvalSpecialist',
  ApprovalList = '/organization/approvalSpecialist/list',
  ApprovalUser = '/organization/approvalSpecialist/department-list',
  ApprovalUserDept = '/organization/departmentApprovalUser',
}

/**
 * @description: 查询部门树
 */
export async function getDepartmentTree(
  params?: DepartmentTreeParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DepartmentTreeModel>(
    {
      url: Api.Tree,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询部门树
 */
export async function getDepartmentEnabledTree(
  params?: DepartmentTreeParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DepartmentTreeModel>(
    {
      url: Api.EnabledTree,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询部门分页列表
 */
export async function getDepartmentPageList(
  params: DepartmentPageListParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DepartmentPageListResultModel>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除部门（批量删除）
 */
export async function deleteDepartment(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.Department,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增部门
 */
export async function addDepartment(department: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Department,
      data: department,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取部门信息
 */
export async function getDepartment(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<DepartmentModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取多个部门信息
 */
export async function getDepartmentMul(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.post<DepartmentModel[]>(
    {
      url: Api.MultiInfo,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新部门
 */
export async function updateDepartment(department: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.Department,
      data: department,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询部门用户
 */
export async function getDepartmentUserList(params?, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.User,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新部门用户
 */
export async function updateDepartmentUser(paramas: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.UpdateUser,
      data: paramas,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改审批专员
 */
export async function updateApprovalSpe(
  paramas: ApprovalModel[],
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<boolean>(
    {
      url: Api.Approval,
      data: paramas,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询审批专员列表
 */
export async function getApprovalList(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<ApprovalModel[]>(
    {
      url: Api.ApprovalList,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询审批专员人员配置
 */
export async function getApprovalUser(departmentId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<ApprovalUserModel[]>(
    {
      url: Api.ApprovalUser,
      params: { departmentId },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改审批专员人员
 */
export async function updateApprovalUser(
  paramas: ApprovalUserParams[],
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<boolean>(
    {
      url: Api.ApprovalUserDept,
      data: paramas,
    },
    {
      errorMessageMode: mode,
    },
  );
}
