import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export interface DepartmentPageListParams {
  name: string; //部门名
  code: string; //编码
  status: number; //手机号码
}
/**
 * @description: Request list interface parameters
 */
export type DepartmentPageListParamsModel = BasicPageParams & DepartmentPageListParams;

export interface DepartmentPageListModel {
  name: string; //部门名
  code: string; //编码
  mobile: string; //手机号码
  remark: string; //备注
}

export interface DepartmentTreeParams {
  name: string; //部门名
  code: string; //编码
  status: number; //状态
}

export interface DepartmentTreeModel {
  id: string; //id
  parentId: string; //父id
  name: string; //部门名
  code: string; //编码
  mobile: string; //手机号码
  remark: string; //备注
  children: DepartmentTreeModel[];
}

export interface DepartmentModel {
  name: string; //部门名
  parentId: string; //父级id
  code: string; //编码
  mobile: string; //手机号码
  remark: string; //备注
  email: string; //邮箱
  website: string; //主页
  address: string; //地址
  sortCode?: number; //排序码
  enabledMark: number; //排序码
  departmentLabel?: any; //部门标签
  departmentType?: number; //组织类型
}

export interface ApprovalModel {
  id?: string;
  name: string; //审批专员名称
  remark: string; //备注
}

export interface ApprovalUserModel {
  id: string;
  departmentId: string; //部门id
  approvalSpecialistId: string; //审批专员id
  approvalSpecialistName: string; //审批专员名称
  userId?: string; //用户id
  userName?: string; //用户名称
  phoneNumber?: string; //手机号码
  emailCount?: string; //邮箱
}

export interface ApprovalUserParams {
  id?: string;
  approvalSpecialistId: string; //审批专员id
  departmentId: string; //部门id
  emailCount?: string; //邮箱
  phoneNumber?: string; //手机号码
  userId: string; //用户id
}

/**
 * @description: Request list return value
 */
export type DepartmentPageListResultModel = BasicFetchResult<DepartmentPageListParams>;
