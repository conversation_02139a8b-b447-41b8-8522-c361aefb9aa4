import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { DeveloperParamsModel, AuthParamsModel } from './model';
import { BasicPageParams } from '/@/api/model/baseModel';

enum Api {
  Page = '/system/developer/page',
  Info = '/system/developer/info',
  Developer = '/system/developer',
  Auth = '/system/developer/auth',
}

/**
 * @description: 查询开发者管理列表（分页）
 */
export async function getDeveloperPageList(
  params?: BasicPageParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DeveloperParamsModel[]>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增开发者
 */
export async function addDeveloper(data: DeveloperParamsModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Developer,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新开发者
 */
export async function updateDeveloper(
  data: DeveloperParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<number>(
    {
      url: Api.Developer,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除开发者（批量删除）
 */
export async function deleteDeveloper(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Developer,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取开发者详情信息
 */
export async function getDeveloperInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<DeveloperParamsModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取开发者接口授权
 */
export async function getDeveloperAuth(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<string[]>(
    {
      url: Api.Auth,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改开发者接口授权
 */
export async function updateDeveloperAuth(data: AuthParamsModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Auth,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
