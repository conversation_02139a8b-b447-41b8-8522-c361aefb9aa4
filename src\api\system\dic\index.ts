import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import {
  DicDetailListParams,
  DicDetailModel,
  DicDetailPageListModel,
  DicDetailPageListResultModel,
  DicDetailPageListSearchModel,
  DicItemModel,
  DicItemPageListModel,
  DicItemPageListParams,
  DicItemPageListResultModel,
  DicItemPageListSearchModel,
  DicDetailTreeModel,
} from './model';
enum Api {
  Item = '/system/dictionary-item',
  ItemInfo = '/system/dictionary-item/info',
  ItemList = '/system/dictionary-item/list',
  ItemPage = '/system/dictionary-item/page',
  ItemDetail = '/system/dictionary-item/detail',
  Detail = '/system/dictionary-detail',
  DetailInfo = '/system/dictionary-detail/info',
  DetailList = '/system/dictionary-detail',
  DetailPage = '/system/dictionary-detail/page',
  DetailTree = '/system/dictionary-item/detail/tree',
  AppMenu = '/app/menu/list',
  itemCate = '/system/dictionary-type',
  itemCateList = '/system/dictionary-type/list',
  dicTree = '/system/dictionary-type/tree-list',
  getPersionMattersList='/wechat/sign/getPersionMattersList',
  getOtcStorePromotion='/wechat/sign/getOtcStorePromotion',
  getOtcStoreTraining='/wechat/sign/getOtcStoreTraining',
  getOtcStoreDisplayCheck='/wechat/sign/getOtcStoreDisplayCheck',
  getVisitLogSetVo='/business/sign/getVisitLogSetVo',
}

/**
 * @description: 查询数据字典项树
 */
export async function getDicTreeList(
  params?: DicItemPageListParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DicItemPageListModel[]>(
    {
      url: Api.dicTree,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询数据字典项树
 */
export async function getPersionMattersList(
  params?: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<any>(
    {
      url: Api.getPersionMattersList,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 驻点促销
 */
export async function getOtcStorePromotion(
  params?: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<any>(
    {
      url: Api.getOtcStorePromotion,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 驻点促销
 */
export async function getOtcStoreTraining(
  params?: any,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<any>(
    {
      url: Api.getOtcStoreTraining,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 驻点促销
 */
export async function getOtcStoreDisplayCheck(
    params?: any,
    mode: ErrorMessageMode = 'modal',
) {
    return defHttp.get<any>(
        {
            url: Api.getOtcStoreDisplayCheck,
            params,
        },
        {
            errorMessageMode: mode,
        },
    );
}
/**
 * @description: 驻点促销
 */
export async function getVisitLogSetVo(
    params?: any,
     mode: ErrorMessageMode = 'modal',
) {
    return defHttp.get<any>(
        {
            url: Api.getVisitLogSetVo,
            params,
        },
        {
            errorMessageMode: mode,
        },
    );
}



/**
 * @description: 查询数据字典项 不分页
 */
export async function getDicItemList(
  params?: DicItemPageListParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DicItemPageListModel[]>(
    {
      url: Api.ItemList,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询数据字典项分页
 */
export async function getDicItemPageList(
  params: DicItemPageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DicItemPageListResultModel>(
    {
      url: Api.ItemPage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除数据字典项（批量删除）
 */
export async function deleteDicItem(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Item,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增数据字典项
 */
export async function addDicItem(dicItem: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Item,
      data: dicItem,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取数据字典项
 */
export async function getDicItem(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<DicItemModel>(
    {
      url: Api.ItemInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取数据字典项
 */
export async function getDicItemDetail(
  id: string,
  values: string,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DicItemModel[]>(
    {
      url: Api.ItemDetail,
      params: { id, values },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新数据字典项
 */
export async function updateDicItem(item: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.Item,
      data: item,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/****************************************************************************************************** */

/**
 * @description: 查询数据字典详情分页
 */
export async function getDicDetailPageList(
  params: DicDetailPageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DicDetailPageListResultModel>(
    {
      url: Api.DetailPage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询数据字典详情列表不分页
 */
export async function getDicDetailList(
  params?: DicDetailListParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DicDetailPageListModel[]>(
    {
      url: Api.DetailList,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * 查询所有功能菜单
 */
export function getAppFuncList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    { url: Api.AppMenu, params },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 删除数据字典详情（批量删除）
 */
export async function deleteDicDetail(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Detail,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增数据字典详情
 */
export async function addDicDetail(dicDetail: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Detail,
      data: dicDetail,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取数据字典详情
 */
export async function getDicDetail(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<DicDetailModel>(
    {
      url: Api.DetailInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新数据字典详情
 */
export async function updateDicDetail(detail: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.Detail,
      data: detail,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取当前数据字典项目下所有字典详情
 */
export async function getDicDetailTree(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<DicDetailTreeModel[]>(
    {
      url: Api.DetailTree,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增数据字典项分类
 */
export async function addDicCate(dicItem: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.itemCate,
      data: dicItem,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新数据字典项分类
 */
export async function updateDicCate(item: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.itemCate,
      data: item,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询数据字典项分类
 */
export async function getDicCateList(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<DicItemPageListModel[]>(
    {
      url: Api.itemCateList,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除数据字典项分类
 */
export async function deleteDicCate(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.itemCate,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
