import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export interface DicItemPageListParams {
  name?: string; //姓名
  code?: string; //编码
}

/**
 * @description: Request list interface parameters
 */
export type DicItemPageListSearchModel = BasicPageParams & DicItemPageListParams;

export interface DicItemPageListModel {
  id: string;
  name: string; //用户名
  code: string; //编码
  remark: string; //备注
}

export interface DicItemModel {
  name: string; //用户名
  code: string; //编码
  sortCode: number; //排序号
  remark: string; //备注
}

/**
 * @description: Request list return value
 */
export type DicItemPageListResultModel = BasicFetchResult<DicItemPageListModel>;

/**************************************************************************************** */

export interface DicDetailPageListParams {
  name?: string; //姓名
  code?: string; //编码
  itemId: string; //项目id
}

export interface DicDetailListParams {
  name?: string; //姓名
  code?: string; //编码
  itemId?: string; //项目id
}

/**
 * @description: Request list interface parameters
 */
export type DicDetailPageListSearchModel = BasicPageParams & DicDetailPageListParams;

export interface DicDetailPageListModel {
  id: string; //id
  name: string; //名称
  code: string; //编码
  remark: string; //备注
  value?: string; //保存值
}

export interface DicDetailModel {
  id: string; //用户名
  name: string; //用户名
  code: string; //编码
  sortCode: number; //排序号
  remark: string; //备注
}

export interface DicDetailTreeModel {
  id: string; //用户名
  name: string; //用户名
  code: string; //编码
  sortCode: number; //排序号
  remark: string; //备注
  children: Array<DicDetailTreeChildrenModel>; //子级
}

export interface DicDetailTreeChildrenModel {
  itemId: string; //项目id
  name: string; //用户名
  code: string; //编码
  sortCode: number; //排序号
  remark: string; //备注
  value: string; //值
}

/**
 * @description: Request list return value
 */
export type DicDetailPageListResultModel = BasicFetchResult<DicDetailPageListModel>;
