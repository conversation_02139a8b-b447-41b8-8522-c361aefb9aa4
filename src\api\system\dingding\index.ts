import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { DepartmentListModel, UserListModel, ConfigParamsModel } from './model';
import { BasicPageParams } from '/@/api/model/baseModel';

enum Api {
  Departments = '/organization/ding-talk/departments',
  Department = '/organization/ding-talk/department',
  Member = '/organization/ding-talk/page',
  SyncDept = '/organization/ding-talk/sync-departments',
  SyncDing = '/organization/ding-talk/sync-user/to-dingtalk',
  SyncSystem = '/organization/ding-talk/sync-user/to-system',
}

/**
 * @description: 查询组织列表
 */
export async function getDepartmentsList(
  params?: BasicPageParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DepartmentListModel>(
    {
      url: Api.Departments,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询用户列表
 */
export async function getUserList(params?: BasicPageParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<UserListModel>(
    {
      url: Api.Member,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description:设置配置信息
 */
export async function updateDepartment(
  params: ConfigParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<boolean>(
    {
      url: Api.Department,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 同步组织
 */
export async function updateSyncDept(departmentId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: `${Api.SyncDept}?departmentId=${departmentId}`,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 同步至钉钉
 */
export async function updateSyncDing(departmentId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: `${Api.SyncDing}?departmentId=${departmentId}`,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 同步至系统
 */
export async function updateSyncSys(departmentId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: `${Api.SyncSystem}?departmentId=${departmentId}`,
    },
    {
      errorMessageMode: mode,
    },
  );
}
