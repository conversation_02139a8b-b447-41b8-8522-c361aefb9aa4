export interface DepartmentListModel {
  id: string; //id
  name: string; //名称
  code: string; //编码
  dingtalkDeptId: string; //钉钉组织Id
}

export interface UserListModel {
  id: string; //id
  name: string; //姓名
  userName: string; //账号
  mobile: string; //手机
  dingtalkUserId: string; //钉钉用户ID
  departmentName: string; //组织
  gender: number; //性别
}

export interface ConfigParamsModel {
  id: string; //id
  dingAppKey: string; //DingAppKey
  dingAppSecret: string; //dingAppSecret
  dingAgentId: string; //dingAgentId
}
