import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import {
  ExcelPageListSearchModel,
  ExcelResultModel,
  ExcelAddParamsModel,
  ExcelNameParamsModel,
  ExcelAuthParamsModel,
  ExcelAuthModel,
} from './model';

enum Api {
  Page = '/system/excel-file/page',
  Info = '/system/excel-file/excel-info',
  ExcelFile = '/system/excel-file',
  AuthInfo = '/system/excel-file/excel-auth-info',
  UpdateExcel = '/system/excel-file/update-excel',
  UpdateAuth = '/system/excel-file/update-excel-auth',
  UpdateName = '/system/excel-file/update-excel-name',
}

/**
 * @description: 查询在线excel列表（分页）
 */
export async function getExcelPageList(
  params?: ExcelPageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<ExcelResultModel[]>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增excel
 */
export async function addExcelFile(data: ExcelAddParamsModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.ExcelFile,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新excel
 */
export async function updateExcel(data: ExcelAddParamsModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.UpdateExcel,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除excel（批量删除）
 */
export async function deleteExcel(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.ExcelFile,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新excel名称
 */
export async function updateExcelName(
  data: ExcelNameParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<number>(
    {
      url: Api.UpdateName,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取excel信息
 */
export async function getExcelInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<ExcelAddParamsModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取excel权限
 */
export async function getExcelAuth(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<ExcelAuthModel[]>(
    {
      url: Api.AuthInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新excel权限
 */
export async function updateExcelAuth(
  data: ExcelAuthParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<number>(
    {
      url: Api.UpdateAuth,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
