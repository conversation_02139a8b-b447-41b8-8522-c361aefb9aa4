import { BasicPageParams } from '/@/api/model/baseModel';

export interface ExcelPageListParams {
  keyword?: string; //关键字
}

export type ExcelPageListSearchModel = BasicPageParams & ExcelPageListParams;

export interface ExcelResultModel {
  id: string;
  fileName: string; //文件名称
  modifyDate: string; //更新时间
  createUserName: string; //创建人
}

export interface ExcelAddParamsModel {
  id?: string;
  jsonContent: string; //文件json
  fileJson: string; //文件大小
  fileName: string; //列表页下载所需数据
}

export interface ExcelNameParamsModel {
  id: string;
  fileName: string; //文件名称
}

export interface ExcelAuthDtoParamsModel {
  authType: number; //类型
  objectId: string; //类型Id
}

export interface ExcelAuthParamsModel {
  excelId: string;
  excelAuthDtoList: ExcelAuthDtoParamsModel[]; //类型Id
}

export interface ExcelAuthModel {
  authType: number; //类型
  objectId: string; //类型Id
  objectName: string; //类型名称
}
