import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import {
  FilePageListSearchModel,
  FileResultModel,
  FileNameParamsModel,
  FileAuthParamsModel,
  FileAuthModel,
} from './model';

enum Api {
  Page = '/system/file-management/page',
  File = '/system/file-management',
  AuthInfo = '/system/file-management/file-auth-info',
  UpdateAuth = '/system/file-management/update-file-auth',
  UpdateName = '/system/file-management/update-file-name',
}

/**
 * @description: 查询文件列表（分页）
 */
export async function getFilePageList(
  params?: FilePageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<FileResultModel[]>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除文件（批量删除）
 */
export async function deleteFile(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.File,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新文件名称
 */
export async function updateFileName(data: FileNameParamsModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.UpdateName,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取文件权限
 */
export async function getFileAuth(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<FileAuthModel[]>(
    {
      url: Api.AuthInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新文件权限
 */
export async function updateFileAuth(data: FileAuthParamsModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.UpdateAuth,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
