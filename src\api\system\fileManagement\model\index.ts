import { BasicPageParams } from '/@/api/model/baseModel';

export interface FilePageListParams {
  keyword?: string; //关键字
}

export type FilePageListSearchModel = BasicPageParams & FilePageListParams;

export interface FileResultModel {
  id: string;
  fileName: string; //文件名称
  fileSize: string | number; //文件大小
  modifyDate: string; //更新时间
  createUserName: string; //创建人
  fileUrl: string; //文件地址
}

export interface FileNameParamsModel {
  id: string;
  fileName: string; //文件名称
}

export interface FileAuthDtoParamsModel {
  authType: number; //类型
  objectId: string; //类型Id
}

export interface FileAuthParamsModel {
  fileManagementId: string;
  fileAuthDtoList: FileAuthDtoParamsModel[];
}

export interface FileAuthModel {
  authType: number; //类型
  objectId: string; //类型Id
  objectName: string; //类型名称
}
