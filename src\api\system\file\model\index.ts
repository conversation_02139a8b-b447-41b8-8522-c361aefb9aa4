import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export interface FilePageListParams {
  fileName?: string; //文件名
  folderId?: string; //文件夹Id
  processId?: string; //流程Id
}

/**
 * 文件列表 返回模型
 */
export interface FilePageListModel {
  id: number;
  folderId: string;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileSuffiex: any;
  fileType: string;
  downloadCount: number;
  remark: string;
}

/**
 * 文件信息  返回模型
 */
export interface FileModel {
  id: number;
  folderId: number;
  fileName: string;
  fileUrl: string;
  fileSize: number;
  fileSuffiex: any;
  fileType: string;
  downloadCount: number;
  remark: string;
}

/**
 * @description: 分页返回 模型
 */
export type FilePageListResultModel = BasicFetchResult<FilePageListModel>;

/**
 * @description: 分页请求参数 模型
 */
export type FilePageListSearchModel = BasicPageParams & FilePageListParams;
