import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';
import { MessageType } from '/@/enums/messageTemplate';
/**
 * @description: 编辑
 */
export async function set(configType: MessageType, params: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: '/system/system-config',
      params: { configType, configJson: JSON.stringify(params) },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取
 */
export async function getInfo(type: MessageType, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: '/system/system-config/info-by-type',
      params: { type },
    },
    {
      errorMessageMode: mode,
    },
  );
}
