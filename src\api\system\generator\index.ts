import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import {
  GeneratorAppModel,
  GeneratorModel,
  SaveDraftGeneratorModel,
  ValidateNameModel,
  ValidateTableModel,
} from './model';
import { GeneratorConfig } from '/@/model/generator/generatorConfig';

enum Api {
  DataFirst = '/system/generator/generator-code/data-first',
  CodeFirst = '/system/generator/generator-code/code-first',
  CodeFirstPreview = '/system/generator/preview-code/code-first',
  DataFirstPreview = '/system/generator/preview-code/data-first',
  SaveDraft = '/system/code-schema',
  ValidateName = '/system/databaselink/table/validate-name',
  ValidateTable = '/system/databaselink/table/validate',
  CodeTemp = '/system/code-schema/page',
  App = '/system/generator/generator-app-code',
  Master = '/system/databaselink/master-info',
  Batch = '/system/generator/generator-code/batch',
}

/**
 * @description: 数据优先 生成代码
 */
export async function dataFirstGeneratorCode(
  data: GeneratorModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<boolean>(
    {
      url: Api.DataFirst,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 界面优先 生成代码
 */
export async function codeFirstGeneratorCode(
  data: GeneratorModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<boolean>(
    {
      url: Api.CodeFirst,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 数据优先 预览代码
 */
export async function dataFirstPreviewCode(
  data: GeneratorConfig,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<any>(
    {
      url: Api.DataFirstPreview,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 界面优先 预览代码
 */
export async function codeFirstPreviewCode(
  data: GeneratorConfig,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<any>(
    {
      url: Api.CodeFirstPreview,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 保存草稿
 */
export async function saveDraftGeneratorCode(
  data: SaveDraftGeneratorModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<boolean>(
    {
      url: Api.SaveDraft,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 更新草稿
 */
export async function updateDraftGeneratorCode(
  data: SaveDraftGeneratorModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<boolean>(
    {
      url: Api.SaveDraft,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 校验表名是否重复
 */
export async function validateTableName(
  params: ValidateNameModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<any>(
    {
      url: Api.ValidateName,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 校验表
 */
export async function validateTable(params: ValidateTableModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<any>(
    {
      url: Api.ValidateTable,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取代码生成器模板
 */
export async function getCodeTemplateList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.CodeTemp,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除代码模板
 */
export async function deleteCodeTemplate(ids, mode: ErrorMessageMode = 'modal') {
  return defHttp.delete(
    {
      url: Api.SaveDraft,
      params: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取代码模板详情信息
 */
export async function getCodeTemplateInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.SaveDraft + '/info',
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 手机端代码生成器
 */
export async function appGeneratorCode(data: GeneratorAppModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.App,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取默认数据库信息
 */
export async function getMasterInfo(mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Master,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 批量生成代码
 */
export async function batchGeneratorCode(data: GeneratorModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Batch,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
