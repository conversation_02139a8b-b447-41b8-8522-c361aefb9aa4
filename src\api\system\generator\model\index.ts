import { GeneratorConfig } from '/@/model/generator/generatorConfig';
import { TableStructureConfig } from '/@/model/generator/tableStructureConfig';

export interface GeneratorModel extends GeneratorConfig {
  frontCode: FrontCode;
  id?: string;
}

export interface GeneratorAppModel extends FrontCode {
  className?: string;
  outputArea?: string;
  outputValue?: string;
  containerCode?: string;
  tagString?: string;
}
export interface AppFuncModel {
  appMenuId?: string;
  id?: string;
  codeSchemaId?: string;
  codes: AppFuncCode;
  formType: number;
  funcDescribe?: string;
  funcModule?: string;
  jsonContent: string;
  menuConfigs: AppFuncMenu;
  remark?: string;
  enabledMark: number;
  isGeneratorCode?: number;
}
export interface AppFuncCode {
  listCode?: string;
  formCode?: string;
  apiCode?: string;
  className?: string;
  configJsonCode?: string;
  outputArea?: string;
}
export interface AppFuncMenu {
  categoryId?: string;
  code?: string;
  icon?: string;
  name?: string;
  remark?: string;
  sortCode?: number;
}
export interface SaveDraftGeneratorModel {
  //功能名称
  name: string;
  //类型：0-数据优先模板 1-界面优先模板 2-简易模板
  type: number;
  //内容
  content: string;
  //功能描述
  remark: string;
  id?: string;
}

export interface FrontCode {
  listCode: string;
  formCode: string;
  apiCode: string;
  modelCode?: string;
  configJsonCode: string;
  workflowPermissionCode?: string; //配置工作流权限数据
  simpleFormCode?: string; //simpleForm页面
}

export interface ValidateNameModel {
  id: string;
  tableNames: string;
}

export interface ValidateTableModel {
  databaseId: string;
  tableStructureConfigs: TableStructureConfig[];
}
