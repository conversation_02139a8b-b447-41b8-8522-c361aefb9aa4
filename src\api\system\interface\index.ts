import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { InterfaceInfoParams, InterfaceListInfo, InterfaceListParams } from './model';

enum Api {
  Tree = '/interface/group/tree',
  Info = '/interface/info',
  List = '/interface/list',
  All = '/interface/tree',
}
/**
 * @description: 查询所有接口-树结构
 */
export async function getAllInterface(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.All,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询所有树结构分组
 */
export async function getInterfaceTree(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.Tree,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询api详情
 */
export async function getInterfaceInfo(
  params: InterfaceInfoParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<InterfaceListInfo>(
    {
      url: Api.Info,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 根据分组id 查询所有api
 */
export async function getInterfaceList(
  params: InterfaceListParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<any>(
    {
      url: Api.List,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
