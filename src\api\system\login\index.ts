import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import {
  getMenuListResultModel,
  GetUserInfoModel,
  KeycloakLoginParams,
  LoginParams,
  LoginResultModel,
  PermissionResultModel,
} from './model';

enum Api {
  Login = '/system/login',
  thirdLogin = '/system/thirdLogin',
  Keycloak = '/system/token',
  Logout = '/system/logout',
  GetUserInfo = '/organization/user/current/info',
  GetPermCode = '/system/authorize/permissions',
  GetUserMenuTree = '/system/menu/tree',
  TestRetry = '/testRetry',
  OA = '/oa/message/list',
  System = '/oa/message/box',
  OANews = '/oa/news/box',
  OASchedule = '/oa/schedule/box',
  ScheduleRead = '/oa/schedule/read',
  ScheduleReadAll = '/oa/schedule/all-read',
  Read = '/oa/news/read',
  workReadAll = '/oa/message/read-all-message',
  schemaReadAll = '/oa/message/read-all-schedule-message',
  Single = '/oa/message/read-message',
  QrLogin = '/system/qrcode-login',
  logoConfig = '/system/logoConfig',
  logoInfoToken = '/system/logoConfig/info',
  logoInfo = '/system/logoConfig/logo-info',
  loginInfo = '/system/loginConfig/info',
  loginConfig = '/system/loginConfig',
  approve = '/workflow/execute/get-approve-state',
  GuestLogin = '/system/guest-login',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: Api.Login,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: third login api
 */
export function thirdLoginApi(params: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: Api.thirdLogin,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: user login api
 */
export function keycloakLoginApi(params: KeycloakLoginParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<LoginResultModel>(
    {
      url: Api.Keycloak,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: getUserInfo
 */
export function getUserInfo() {
  return defHttp.get<GetUserInfoModel>({ url: Api.GetUserInfo }, { errorMessageMode: 'none' });
}

export function getPermCode() {
  return defHttp.get<PermissionResultModel>({ url: Api.GetPermCode });
}

export function doLogout() {
  return defHttp.post({ url: Api.Logout });
}

export function testRetry() {
  return defHttp.get(
    { url: Api.TestRetry },
    {
      retryRequest: {
        isOpenRetry: true,
        count: 5,
        waitTime: 1000,
      },
    },
  );
}

/**
 * @description: Get user menu based on id
 */

export const getMenuList = () => {
  return defHttp.get<getMenuListResultModel>({ url: Api.GetUserMenuTree });
};

export function getOaMessage(params) {
  return defHttp.get(
    { url: Api.OA, params },
    {
      withToken: false,
    },
  );
}
// 系统通知
export function getSystemMessage(data, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    { url: Api.System, params: { ...data, type: 4, enabledMark: 1 } },
    {
      errorMessageMode: mode,
      withToken: false,
    },
  );
}

export function getScheduleMsg(params) {
  return defHttp.get(
    { url: Api.OASchedule, params },
    {
      withToken: false,
    },
  );
}

export function setScheduleRead(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.put(
    {
      url: Api.ScheduleRead,
      params: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export function setScheduleReadAll(mode: ErrorMessageMode = 'modal') {
  return defHttp.put(
    {
      url: Api.ScheduleReadAll,
    },
    {
      errorMessageMode: mode,
    },
  );
}

export function getOaNews(data, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    { url: Api.OANews, params: { limit: 1, size: 10, ...data, enabledMark: 1 } },
    {
      errorMessageMode: mode,
      withToken: false,
    },
  );
}
export function setOaRead(id, mode: ErrorMessageMode = 'modal') {
  return defHttp.put(
    { url: Api.Read, params: id },
    {
      errorMessageMode: mode,
    },
  );
}

export function setWorkReadAll(mode: ErrorMessageMode = 'modal') {
  return defHttp.put(
    { url: Api.workReadAll },
    {
      errorMessageMode: mode,
    },
  );
}

export function setSchemaReadAll(mode: ErrorMessageMode = 'modal') {
  return defHttp.put(
    { url: Api.schemaReadAll },
    {
      errorMessageMode: mode,
    },
  );
}

export function setSingleRead(id, mode: ErrorMessageMode = 'modal') {
  return defHttp.put(
    { url: Api.Single, params: { id } },
    {
      errorMessageMode: mode,
    },
  );
}

export function getOauthAuthorizeUrl(source: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    { url: Api.QrLogin, params: { source } },
    {
      errorMessageMode: mode,
    },
  );
}

export function setLogoConfig(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.put(
    { url: Api.logoConfig, params: params },
    {
      errorMessageMode: mode,
    },
  );
}

export function getLogoInfoToken(mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    { url: Api.logoInfoToken },
    {
      errorMessageMode: mode,
    },
  );
}

export function getLogoInfo(mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    { url: Api.logoInfo },
    {
      errorMessageMode: mode,
    },
  );
}
export function getLoginInfo(mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    { url: Api.loginInfo },
    {
      errorMessageMode: mode,
    },
  );
}
export function setLoginConfig(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.put(
    { url: Api.loginConfig, params: params },
    {
      errorMessageMode: mode,
    },
  );
}
//判断流程是否审批过
export function getApproveState(taskId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    { url: Api.approve, params: { taskId } },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: user login api
 */
export function guestLogin(mode: ErrorMessageMode = 'modal') {
  return defHttp.post<string>(
    {
      url: Api.GuestLogin,
    },
    {
      errorMessageMode: mode,
    },
  );
}
