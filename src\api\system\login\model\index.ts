import { RouteMeta } from 'vue-router';
import { DesktopData } from '/@/model/desktop/designer';
/**
 * @description: Login interface parameters
 */
export interface LoginParams {
  userName: string;
  password: string;
  deviceType?: number;
}

/**
 * @description: keycloak Login interface parameters
 */
export interface KeycloakLoginParams {
  token: string;
  deviceType?: number;
}

export interface RoleInfo {
  name: string;
  id: string;
  code: string;
}

export type DepartmentInfo = RoleInfo;

export type PostInfo = RoleInfo;
/**
 * @description: Login interface return value
 */
export interface LoginResultModel {
  token: string;
}

/**
 * @description: Permissions interface retrun value
 */
export interface MenuAuthModel {
  menuId: string;
  buttonAuthCode: string[];
  columnAuthCode: string[];
  formAuthCode: string[];
}

/**
 * @description: Permissions interface retrun value
 */
export interface PermissionResultModel {
  postId: string;
  departmentId: string;
  postName: string;
  departmentName: string;
  roleName: string;
  roleId: string;
  menuAuthList: MenuAuthModel[];
  desktopSchema: DesktopData;
}

/**
 * @description: Get user information return value
 */
export interface GetUserInfoModel {
  // 用户id
  id: string;
  // 真实名字
  name: string;
  // 编码
  code: string;
  // 手机
  mobile: string;
  //角色
  roles: RoleInfo[];
  // 用户名
  userName: string;
  // 头像
  avatar: string;
  //部门id
  departmentId: string | number;
  //所有部门
  departments: DepartmentInfo[];
  //所有部门
  posts: PostInfo[];
  //备注
  remark: string;
  //邮箱
  email: string;
  //地址
  address: string;
  //首页地址
  homePath: string;
}

export interface RouteItem {
  path: string;
  component: any;
  meta: RouteMeta;
  name?: string;
  alias?: string | string[];
  redirect?: string;
  caseSensitive?: boolean;
  children?: RouteItem[];
}

/**
 * @description: Get menu return value
 */
export type getMenuListResultModel = RouteItem[];
