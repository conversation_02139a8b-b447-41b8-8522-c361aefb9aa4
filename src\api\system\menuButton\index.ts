import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { MenuButtonModel } from './model';

enum Api {
  List = '/system/menu-button/list',
  Button = '/system/menu-button',
  Column = '/system/menu-colum/list',
  Field = '/system/menu-form/list',
  AppButton = '/app/menu/button-list',
  AppColumn = '/app/menu/column-list',
  AppField = '/app/menu/form-list',
}

/**
 * @description: 查询菜单按钮列表
 */
export async function getMenuButtonList(menuId = '0', mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.List,
      params: { menuId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询App菜单按钮列表
 */
export async function getAppMenuButtonList(menuId = '0', mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.AppButton,
      params: { menuId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询菜单字段列表
 */
export async function getMenuColumnList(menuId = '0', mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Column,
      params: { menuId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询App菜单字段列表
 */
export async function getAppMenuColumnList(menuId = '0', mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.AppColumn,
      params: { menuId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询菜单表单列表
 */
export async function getMenuFieldList(menuId = '0', mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Field,
      params: { menuId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询App菜单表单列表
 */
export async function getAppMenuFieldList(menuId = '0', mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.AppField,
      params: { menuId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 删除菜单按钮（批量删除）
 */
export async function deleteMenuButton(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Button,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增菜单按钮
 */
export async function addMenuButton(menuButton: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Button,
      data: menuButton,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取菜单信息
 */
export async function getMenuButton(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<MenuButtonModel>(
    {
      url: Api.Button,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新菜单
 */
export async function updateMenu(menuButton: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.Button,
      params: menuButton,
    },
    {
      errorMessageMode: mode,
    },
  );
}
