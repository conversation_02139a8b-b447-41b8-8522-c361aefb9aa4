import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';
import { ListModel, OaBasicPageParams, Params } from './model';

enum Api {
  Oa = '/oa/news',
  Info = '/oa/news/info',
  ChangeStatus = '/oa/news/change-status',
}

/**
 * @description: 获取（分页）
 */
export async function getList(params?: OaBasicPageParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<ListModel>(
    {
      url: Api.Oa,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除（批量删除）
 */
export async function deleteOa(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Oa,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增
 */
export async function add(params: Params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Oa,
      data: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 编辑
 */
export async function edit(params: Params, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.Oa,
      data: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取详情信息
 */
export async function getInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 发布或者下架
 */
export async function changeStatus(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<string>(
    {
      url: Api.ChangeStatus,
      data: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
