/**
 * @description: Request list interface parameters
 */

import { OaType } from '/@/enums/oa';
export interface OaBasicPageParams {
  limit: number;
  size: number;
  type: OaType;
  keyword?: string;
}
export interface ListModel {
  id: number;
  code: string;
  name: string;
  currentNumber: string;
  formatJson: string;
  sortCode: number;
  description: string;
  createDate: string;
  createUserName: string;
}

export interface Params {
  authorName: String; //作者
  briefHead: String; //标题
  category: String; //栏目
  compileName: String; //编辑
  keyword: String; //关键字
  newsContent: String; //内容
  releaseTime: String; //发布时间
  tagWord: String; //tag词
  typeId: OaType;
}
