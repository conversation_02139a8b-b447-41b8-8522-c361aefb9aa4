import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { ReportPageListParamsModel, ReportPageListResultModel, ReportPageListModel } from './model';

enum Api {
  Page = '/report/page',
  List = '/report/list',
  Report = '/report',
  ReleasePage = '/report/release/page',
  Release = '/report/release',
  ReleaseInfo = '/report/release/info',
  ProfessionalPage = '/report/professionalReport/page',
  ProfessionalReport = '/report/professionalReport',
  ProfessionalReleaseInfo = '/report/professionalReport/info',
  ReleaseMenu = '/report/release/menu-together-delete',
}

/************  报表设计 ************* /

/**
 * @description: 查询报表设计分页列表
 */
export async function getReportPageList(
  params: ReportPageListParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<ReportPageListResultModel>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询报表设计列表
 */
export async function getReportList(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<ReportPageListModel[]>(
    {
      url: Api.List,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除报表设计（批量删除）
 */
export async function deleteReport(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Report,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/************  报表发布 ************* /
 

/**
 * @description: 查询报表发布分页列表
 */
export async function getReportReleasePage(
  params: ReportPageListParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<any>(
    {
      url: Api.ReleasePage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询报表发布详情
 */
export async function getReportReleaseInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.ReleaseInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除报表发布（批量删除）
 */
export async function deleteReportRelease(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Release,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 删除报表和菜单
 */
export async function deleteReportAndMenu(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.ReleaseMenu,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增报表发布
 */
export async function addReportRelease(data: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Release,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改报表发布
 */
export async function updateReportRelease(data: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.Release,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/************  专业报表 ************* /

/**
 * @description: 查询专业报表报表设计分页列表
 */
export async function getProfessionalPageList(
  params: ReportPageListParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<ReportPageListResultModel>(
    {
      url: Api.ProfessionalPage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 删除专业报表设计（批量删除）
 */
export async function deleteProfessionalReport(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.ProfessionalReport,
      data: id,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增专业报表发布
 */
export async function addProfessionalReport(data: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.ProfessionalReport,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改专业报表发布
 */
export async function updateProfessionalReport(data: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.ProfessionalReport,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询专业报表发布详情
 */
export async function getProfessionalInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any>(
    {
      url: Api.ProfessionalReleaseInfo,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
