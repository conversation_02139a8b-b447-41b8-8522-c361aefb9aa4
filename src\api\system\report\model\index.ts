import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';
import { ReportCategory } from '/@/enums/reportEnum';

export interface ReportPageListParams {
  order?: string; //排序方式
  orderField?: string; //排序字段
}
/**
 * @description: Request list interface parameters
 */
export type ReportPageListParamsModel = BasicPageParams & ReportPageListParams;

export interface ReportPageListModel {
  id: string; //唯一标识
  name: string; //角色名
  code: string; //编号
  sortCode: number; //排序号
  remark: string; //备注
  dataType: ReportCategory; //类型
}

export interface ReportPageModel {
  currentPage: number; //当前页码
  pageSize: number; //每页大小
  totalPage: number; //总共页码
}

/**
 * @description: Request list return value
 */
export type ReportPageListResultModel = BasicFetchResult<ReportPageListModel> & ReportPageModel;
