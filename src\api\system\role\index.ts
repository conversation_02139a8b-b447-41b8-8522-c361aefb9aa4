import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import {
  RoleInfo,
  RoleList,
  RoleModel,
  RolePageListParamsModel,
  RolePageListResultModel,
  RoleSetAuthParams,
  RoleUserModel,
} from './model';

enum Api {
  Page = '/organization/role/page',
  List = '/organization/role/list',
  Role = '/organization/role',
  Info = '/organization/role/info',
  RoleStatus = '/organization/role/status',
  RoleUser = '/organization/role/user',
  Auth = '/organization/role/auth',
  MultiInfo = '/organization/role/info/multi',
  Interface = '/interface/auth',
  Desktop = '/desktop/relation',
  AppMenu = '/app/menu/simple-tree',
  AppMenuAuth = '/organization/role/app-auth',
}

/**
 * @description: 查询角色分页列表
 */
export async function getRolePageList(
  params: RolePageListParamsModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<RolePageListResultModel>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询角色接口授权 列表
 */
export async function getRoleInterfaceList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Interface + '/role',
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询角色用户
 */
export async function getRoleUser(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<RoleUserModel[]>(
    {
      url: Api.RoleUser,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询角色所有 不分页
 */
export async function getRoleAllList(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<RolePageListResultModel>(
    {
      url: Api.List,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除角色（批量删除）
 */
export async function deleteRole(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Role,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增角色
 */
export async function addRole(role: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Role,
      params: role,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 新增角色接口授权
 */
export async function addRoleInterface(
  roleId: string,
  interfaceIds: string[],
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<number>(
    {
      url: Api.Interface,
      data: {
        roleId,
        interfaceIds,
      },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增角色首页授权
 */
export async function addRoleHomeAuth(
  roleId: string,
  desktopIds: string[],
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<number>(
    {
      url: Api.Desktop,
      data: {
        roleId,
        desktopIds,
      },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询角色首页授权
 */
export async function getRoleHomeAuth(roleId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Desktop + '/role',
      params: { roleId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询角色个人首页授权
 */
export async function getRolePrivateHome(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Desktop + '/current-desktops',
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 设置个人首页授权
 */
export async function setPrivateHome(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post(
    {
      url: '/desktop/user-relation',
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增角色
 */
export async function addRoleUser(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.RoleUser,
      data: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 角色设置权限
 */
export async function RoleSetAuth(data: RoleSetAuthParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.Auth,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 角色设置App功能权限
 */
export async function RoleSetAppAuth(data: RoleSetAuthParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.AppMenuAuth,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取角色权限
 */
export async function getRoleAuth(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Auth,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取APP功能
 */
export async function getAppMenu(mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.AppMenu,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取APP功能权限
 */
export async function getAppMenuAuth(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.AppMenuAuth,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取角色信息
 */
export async function getRole(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<RoleModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新角色
 */
export async function updateRole(role: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.Role,
      data: role,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新角色状态
 */
export async function updateRoleStatus(
  id: string,
  status: number,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<number>(
    {
      url: Api.RoleStatus,
      data: {
        id,
        enabledMark: status,
      },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询角色列表
 */
export async function getRoleList(
  params: {
    keyword: string;
  },
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<RoleList>(
    {
      url: Api.List,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 批量获取角色信息
 */
export async function getRoleMulti(ids: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<RoleInfo[]>(
    {
      url: Api.MultiInfo,
      params: { ids },
    },
    {
      errorMessageMode: mode,
    },
  );
}
