import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';

export interface RolePageListParams {
  name: string; //角色名
  code: string; //编号
  status: number; //状态
}
export interface RoleItem {
  id: string;
  name: string; //角色名
  code: string; //编号
  status: number; //状态
  count: number; //角色人数
}
export interface RoleInfo {
  id: string;
  name: string; //角色名
  code: string; //编号
  count: number; //角色人数
}
/**
 * @description: Request list interface parameters
 */
export type RolePageListParamsModel = BasicPageParams & RolePageListParams;

export interface RolePageListModel {
  name: string; //角色名
  code: string; //编号
  sortCode: number; //排序号
  remark: string; //备注
}

export interface RoleModel {
  name: string; //角色名
  code: string; //编号
  sortCode: number; //排序号
  remark: string; //备注
  enabledMark: number; //是否启用
}

export interface RoleUserModel {
  id: string; //id
  name: string; //用户名称
  count: number; //角色人数
}

export interface RoleSetAuthParams {
  id: string;
  menuIds: string[]; //菜单ids
  buttonIds: string[]; //按钮ids
  columnIds: string[];
  formIds: string[];
}

/**
 * @description: Request list return value
 */
export type RolePageListResultModel = BasicFetchResult<RolePageListParams>;
export type RoleList = Array<RoleItem>;
