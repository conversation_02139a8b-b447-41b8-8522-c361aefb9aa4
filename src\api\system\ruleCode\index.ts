import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { CodePageListSearchModel, CodeResultModel, CodeNameParamsModel } from './model';

enum Api {
  Page = '/liteflow/code/page',
  RuleCode = '/liteflow/code',
  Info = '/liteflow/code/info',
}

/**
 * @description: 查询规则代码（分页）
 */
export async function getRuleCodePageList(
  params?: CodePageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<CodeResultModel[]>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除规则代码（批量删除）
 */
export async function deleteRuleCode(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.RuleCode,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改规则代码
 */
export async function updateRuleCode(data: CodeNameParamsModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.RuleCode,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取规则代码详情
 */
export async function getRuleCodeInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<CodeNameParamsModel[]>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增规则代码
 */
export async function addRuleCode(data: CodeNameParamsModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.RuleCode,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
