import { BasicPageParams } from '/@/api/model/baseModel';

export interface CodePageListParams {
  keyword?: string; //关键字
}

export type CodePageListSearchModel = BasicPageParams & CodePageListParams;

export interface CodeResultModel {
  id: string;
  codeNumber: string; //规则代码编号
  methodName: string; //规则代码方法名
  remark: string; //备注
  createUserName: string; //创建人
  createDate: string; //创建时间
  modifyUserName: string; //最后修改人
  modifyDate: string; //最后修改时间
}

export interface CodeNameParamsModel {
  id?: string;
  codeNumber: string; //规则代码编号
  methodName: string; //规则代码方法名
  remark: string; //备注
  codeData: string; //代码
}
