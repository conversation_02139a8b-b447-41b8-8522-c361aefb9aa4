import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { ScheduleListModal, ScheduleAddModel, ScheduleUpdateModel } from './model';

enum Api {
  Schedule = '/oa/schedule',
  Info = '/oa/schedule/info',
}

/**
 * @description: 查询日程列表
 */
export async function getScheduleList(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<ScheduleListModal[]>(
    {
      url: Api.Schedule,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除日程
 */
export async function deleteSchedule(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.Schedule,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增日程
 */
export async function addSchedule(Schedule: ScheduleAddModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Schedule,
      data: Schedule,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取部门信息
 */
export async function getScheduleInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<ScheduleUpdateModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新部门
 */
export async function updateSchedule(
  Schedule: ScheduleUpdateModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<boolean>(
    {
      url: Api.Schedule,
      data: Schedule,
    },
    {
      errorMessageMode: mode,
    },
  );
}
