import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { ScriptPageListSearchModel, ScriptResultModel, ScriptParamsModel } from './model';

enum Api {
  Page = '/system/script-demo/page',
  List = '/system/script-demo/list',
  ScriptDemo = '/system/script-demo',
  Info = '/system/script-demo/info',
}

/**
 * @description: 查询示例脚本（分页）
 */
export async function getScriptDemoPageList(
  params?: ScriptPageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<ScriptResultModel[]>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询示例脚本（不分页）
 */
export async function getScriptDemoList(
  params?: ScriptPageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<ScriptResultModel[]>(
    {
      url: Api.List,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除示例脚本（批量删除）
 */
export async function deleteScriptDemo(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.ScriptDemo,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改示例脚本
 */
export async function updateScriptDemo(data: ScriptParamsModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.ScriptDemo,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取示例脚本详情
 */
export async function getScriptDemoInfo(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<ScriptParamsModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增示例脚本
 */
export async function addScriptDemo(data: ScriptParamsModel, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.ScriptDemo,
      data,
    },
    {
      errorMessageMode: mode,
    },
  );
}
