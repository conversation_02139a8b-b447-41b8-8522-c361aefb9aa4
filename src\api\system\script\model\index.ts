import { BasicPageParams } from '/@/api/model/baseModel';

export interface ScriptPageListParams {
  keyword?: string; //关键字
}

export type ScriptPageListSearchModel = BasicPageParams & ScriptPageListParams;

export interface ScriptResultModel {
  id: string;
  code: string; //示例代码编号
  name: string; //示例代码名
  remark: string; //备注
  createUserName: string; //创建人
  createDate: string; //创建时间
  modifyUserName: string; //最后修改人
  modifyDate: string; //最后修改时间
}

export interface ScriptParamsModel {
  id?: string;
  code: string; //示例代码编号
  name: string; //示例代码名
  remark: string; //备注
  scriptContent: string; //代码
}
