import { defHttp } from '/@/utils/http/axios';
import { BasicPageParams } from '/@/api/model/baseModel';
import { ErrorMessageMode } from '/#/axios';

enum Api {
  List = '/system/subsystem/list',
  Page = '/system/subsystem',
  PageList = '/system/subsystem/page',
  AllList = '/system/subsystem/all-list',
}

/**
 * @description: 查询当前用户授权子系统(不分页包括主系统)
 */
export async function getSubSystemList(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any[]>(
    {
      url: Api.List,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询所有系统(不分页)
 */
export async function getSystemAllList(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any[]>(
    {
      url: Api.List,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询当前用户授权子系统(分页)
 */
export async function getSubSystemPage(params: BasicPageParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any[]>(
    {
      url: Api.PageList,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询当前用户授权子系统(不分页)
 */
export async function getSubSystemListPage(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<any[]>(
    {
      url: Api.Page,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 删除子系统
 */
export async function deleteSubSystem(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.Page,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 新增子系统
 */
export async function addSubSystem(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 更新子系统
 */
export async function updateSubSystem(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
