import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode, UploadFileParams } from '/#/axios';
import {
  UserInfo,
  UserList,
  UserModel,
  UserPageListResultModel,
  UserPageListSearchModel,
  OnlinePageListSearchModel,
  OnlineUserPageListResultModel,
  OffOnlineUserParams,
} from './model';
import { useGlobSetting } from '/@/hooks/setting';

const globSetting = useGlobSetting();

enum Api {
  Page = '/organization/user/page',
  List = '/organization/user/list',
  Info = '/organization/user/info',
  UpdateInfo = '/organization/user/update/info',
  UpdatePassword = '/organization/user/update/password',
  UpdateAvatar = '/organization/user/update/avatar',
  User = '/organization/user',
  UserUpdateUserPostAndRole = '/organization/user/updateUserPostAndRole',
  MultiInfo = '/organization/user/info/multi',
  Password = '/organization/user/reset-password',
  enabled = '/organization/user/enabled',
  Online = '/organization/user/online-users/page',
  OffOnline = '/organization/user/offline-users',
  Profile = '/organization/user/get-user-organization-info',
}

/**
 * @description: 查询用户分页列表
 */
export async function getUserPageList(
  params: UserPageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<UserPageListResultModel>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除用户（批量删除）
 */
export async function deleteUser(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<number>(
    {
      url: Api.User,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增用户
 */
export async function addUser(user: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.User,
      params: user,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取用户信息
 */
export async function getUser(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<UserModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 重置用户密码
 */
export async function resetUserPassword(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.put(
    {
      url: Api.Password,
      data: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新用户
 */
export async function updateUser(user: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.User,
      params: user,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新用户岗位和角色
 */
export async function updateUserPostAndRole(user: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.UserUpdateUserPostAndRole,
      params: user,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新当前登录用户信息
 */
export async function updateUserInfo(user: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.UpdateInfo,
      params: user,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 修改用户密码
 */
export async function updatePassword(user: Recordable, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<number>(
    {
      url: Api.UpdatePassword,
      params: user,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 更新头像
 */
export function uploadAvatar(
  params: UploadFileParams,
  onUploadProgress: (progressEvent: ProgressEvent) => void,
) {
  return defHttp.uploadFile<string>(
    {
      url: Api.UpdateAvatar,
      baseURL: globSetting.apiUrl,
      method: 'POST',
      onUploadProgress,
    },
    params,
  );
}

/**
 * @description: 批量获取用户信息
 */
export async function getUserMulti(ids: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<UserInfo[]>(
    {
      url: Api.MultiInfo,
      data: ids.split(','),
    },
    {
      errorMessageMode: mode,
    },
  );
}

export async function getUserList(
  params: UserPageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<UserList>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询所有人员信息
 */
export async function getAllUserList(mode: ErrorMessageMode = 'modal') {
  return defHttp.get<UserInfo[]>(
    {
      url: Api.List,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 启用-禁用用户
 */
export async function userEnabled(id, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.enabled + '?id=' + id,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询在线用户分页列表
 */
export async function getOnlineUserPageList(
  params: OnlinePageListSearchModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<OnlineUserPageListResultModel>(
    {
      url: Api.Online,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 下线用户
 */
export async function OffOnlineUser(params: OffOnlineUserParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<number>(
    {
      url: Api.OffOnline,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询用户个人资料
 */
export async function getUserProfile(mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.Profile,
    },
    {
      errorMessageMode: mode,
    },
  );
}
