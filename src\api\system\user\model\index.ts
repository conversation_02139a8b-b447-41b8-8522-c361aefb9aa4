import { BasicPageParams, BasicFetchResult } from '/@/api/model/baseModel';
import { GenderEnum } from '/@/enums/userEnum';

export interface UserPageListParams {
  name?: string; //姓名
  mobile?: string; //手机号码
  userName?: string; //账户
  code?: number; //排序号
}

/**
 * @description: Request list interface parameters
 */
export type UserPageListSearchModel = BasicPageParams & UserPageListParams;

export interface UserPageListModel {
  userName: string; //用户名
  name: string; //姓名
  mobile: string; //手机号码
  email: string; //邮箱
  sortCode: number; //排序号
  remark: string; //备注
}

export interface UserModel {
  userName: string; //用户名
  name: string; //姓名
  nickName: string; //昵称
  mobile: string; //手机号码
  avatar: string; //头像
  email: string; //邮箱
  address: string; //地址
  longitude: number; //经度
  latitude: number; //纬度
  sortCode: number; //排序号
  departmentIds: string; //所属机构
  gender: number; //性别
  birthDate: string; //生日
  qqNumber: string; //QQ
  wechatNumber: string; //微信
  remark: string; //备注
  roles: any; //角色信息
  posts: any; //岗位信息
  chargeDepartments: any; //负责部门
}

/**
 * @description: Request list return value
 */
export type UserPageListResultModel = BasicFetchResult<UserPageListModel>;

export interface UserInfo {
  name: string; //姓名
  id: string; //ID
  code: string; //编码
  gender: GenderEnum; //性别
  mobile?: string; //联系电话
  email?: string; //邮箱
}

export type UserList = BasicFetchResult<UserInfo>;

export interface OnlineUserPageListParams {
  device?: string; //登录设备
}

/**
 * @description: Request list interface parameters
 */
export type OnlinePageListSearchModel = BasicPageParams & OnlineUserPageListParams;

export interface OnlineUserModel {
  userName: string; //用户名
  name: string; //人员名称
  departmentNames: string; //所属组织
  onlineTime: string; //在线时长
}

/**
 * @description: Request list return value
 */
export type OnlineUserPageListResultModel = BasicFetchResult<OnlineUserModel>;

export interface OffOnlineUserParams {
  device: string; //设备
  userIds: string[]; //人员Id
}
