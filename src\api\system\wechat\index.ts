import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { DepartmentListModel, UserListModel } from './model';
import { BasicPageParams } from '/@/api/model/baseModel';

enum Api {
  Departments = '/organization/wechat/departments',
  Member = '/organization/wechat/page',
  SyncDept = '/organization/wechat/sync-departments',
  SyncUser = '/organization/wechat/sync-user',
}

/**
 * @description: 查询组织列表
 */
export async function getDepartmentsList(
  params?: BasicPageParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<DepartmentListModel>(
    {
      url: Api.Departments,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 查询成员列表
 */
export async function getMemberList(params?: BasicPageParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<UserListModel>(
    {
      url: Api.Member,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 同步组织
 */
export async function updateSyncDept(departmentId?: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.SyncDept + `?departmentId=${departmentId}`,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 同步成员
 */
export async function updateSyncUser(departmentId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: `${Api.SyncUser}?departmentId=${departmentId}`,
    },
    {
      errorMessageMode: mode,
    },
  );
}
