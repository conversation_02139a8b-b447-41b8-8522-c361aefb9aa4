import { defHttp } from '/@/utils/http/axios';
import { ErrorMessageMode } from '/#/axios';
/**
 * @description: 导出指定数据同步文件
 */
export async function exportData(params: any, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<String>(
    {
      url: '/version/datasync/export',
      data: params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 导出全部数据同步文件
 */
export async function exportone(type: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<String>(
    {
      url: '/version/datasync/exportone',
      params: { type },
    },
    {
      errorMessageMode: mode,
    },
  );
}
