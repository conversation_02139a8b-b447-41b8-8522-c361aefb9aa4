import {
  WorkflowModel,
  HistoryModel,
  WorkflowPageParams,
  WorkflowPageResult,
  ChangeResultModel,
} from './model/index';
import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';
import { ProcessJsonModel } from '/@/model/workflow/workflowConfig';

enum Api {
  Page = '/workflow/schema/page',
  List = '/workflow/schema/list',
  Info = '/workflow/schema/info',
  Design = '/workflow/schema/design',
  WorkflowSchema = '/workflow/schema',
  ExportDesign = '/workflow/schema/export',
  DisabledDesign = '/workflow/schema/disabled',
  EnabledDesign = '/workflow/schema/enabled',
  HistoryList = '/workflow/schema-history/list',
  HistorySetCurrent = '/workflow/schema-history/set-current',
  PreviewProcess = '/workflow/execute/preview',
}
/**
 * @description: 导出流程设计
 */
export async function exportDesign(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<WorkflowPageResult>(
    {
      url: Api.ExportDesign,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询Workflow分页列表
 */
export async function getDesignPage(params: WorkflowPageParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<WorkflowPageResult>(
    {
      url: Api.Page,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 获取Workflow信息
 */
export async function getDesignInfo(id: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<WorkflowModel>(
    {
      url: Api.Info,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 新增工作流程设计
 */
export async function addDesign(
  processJsonModel: ProcessJsonModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.post<boolean>(
    {
      url: Api.WorkflowSchema,
      params: processJsonModel,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 编辑工作流程设计
 */
export async function editDesign(
  processJsonModel: ProcessJsonModel,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<boolean>(
    {
      url: Api.WorkflowSchema,
      params: processJsonModel,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 禁用工作流程设计
 */
export async function disabledDesign(schemaId: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.DisabledDesign,
      params: { schemaId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 启用工作流程设计
 */
export async function enabledDesign(schemaId: String, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.EnabledDesign,
      params: { schemaId },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除Workflow（批量删除）
 */
export async function deleteDesign(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.WorkflowSchema,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 获取历史记录列表
 */
export async function getHistory(schemaId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<Array<HistoryModel>>(
    {
      url: Api.HistoryList,
      params: { schemaId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 更新到此版本
 */
export async function updateHistorySetCurrent(
  schemaId: string,
  id: string,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<ChangeResultModel[]>(
    {
      url: Api.HistorySetCurrent,
      params: { schemaId, id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 预览流程
 */
export async function getPreviewProcess(schemaId: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<string>(
    {
      url: Api.PreviewProcess,
      params: { schemaId },
    },
    {
      errorMessageMode: mode,
    },
  );
}
