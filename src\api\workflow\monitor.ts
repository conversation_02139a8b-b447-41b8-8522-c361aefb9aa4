import {
  WorkflowPageParams,
  WorkflowPageResult,
  AllNodesParams,
  AllNodesModal,
  ChangeNodesParams,
} from './model/index';
import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

enum Api {
  ProcessMonitorPage = '/workflow/execute/process-monitor/page',
  SetSuspended = '/workflow/execute/set-suspended',
  ProcessMonitorDelete = '/workflow/execute/process-monitor/delete',
  Cancel = '/workflow/execute/cancel-process',
  AllNodes = '/workflow/execute/all-process-node',
  Change = '/workflow/execute/process-change',
}

/**
 * @description: 查询流程监控列表
 */
export async function getProcessMonitorPage(
  params: WorkflowPageParams,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.get<WorkflowPageResult>(
    {
      url: Api.ProcessMonitorPage,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 将流程挂起或者恢复
 */

export async function postSetSuspended(processId, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.SetSuspended,
      params: { processId },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 删除流程监控
 */

export async function deleteWorkflow(processId, mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.ProcessMonitorDelete,
      params: { processId },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 终止流程
 */

export async function cancelProcess(processId, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Cancel,
      params: { processId },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description:查询可以变更节点
 */
export async function getAllProcessNodes(params: AllNodesParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<AllNodesModal[]>(
    {
      url: Api.AllNodes,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 变更流程节点
 */
export async function changeNode(params: ChangeNodesParams, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.Change,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
