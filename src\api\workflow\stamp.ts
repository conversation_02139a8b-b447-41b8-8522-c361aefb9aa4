import { StampPageResult } from './model/index';
import { defHttp } from '/@/utils/http/axios';

import { ErrorMessageMode } from '/#/axios';

import { StampType } from '/@/enums/workflowEnum';

enum Api {
  StampListAll = '/system/stamp/page-one',
  StampList = '/system/stamp/page',
  Stamp = '/system/stamp',
  EnabledStamp = '/system/stamp/enabled',
  SetDefaultStamp = '/system/stamp/set-default',
  AddMaintain = '/system/stamp/add-maintain',
  AddMember = '/system/stamp/add-member',
  GetMember = '/system/stamp/member',
}

/**
 * @description: 查询签章列表
 */

export async function getStampPage(type: StampType, params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<StampPageResult>(
    {
      url: Api.StampList,
      params: { stampType: type, ...params },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询签章列表
 */

export async function getStampList(params, mode: ErrorMessageMode = 'modal') {
  return defHttp.get<StampPageResult>(
    {
      url: Api.StampListAll,
      params,
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 新增签章
 */

export async function postStamp(type: StampType, params, mode: ErrorMessageMode = 'modal') {
  return defHttp.post<boolean>(
    {
      url: Api.Stamp,
      params: { ...params, stampType: type },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 编辑签章
 */

export async function putStamp(
  id: string,
  type: StampType,
  params,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<boolean>(
    {
      url: Api.Stamp,
      params: { ...params, id, stampType: type },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 删除签章
 */
export async function deleteStamp(ids: string[], mode: ErrorMessageMode = 'modal') {
  return defHttp.delete<boolean>(
    {
      url: Api.Stamp,
      data: ids,
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 启用或者禁用签章
 */
export async function EnabledStamp(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.EnabledStamp,
      data: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 设定为默认签章
 */
export async function setDefaultStamp(id: string, mode: ErrorMessageMode = 'modal') {
  return defHttp.put<boolean>(
    {
      url: Api.SetDefaultStamp,
      data: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 添加维护成员
 */
export async function addMaintain(
  id: string,
  userIds: Array<string>,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<boolean>(
    {
      url: Api.AddMaintain,
      data: { id, userIds },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: 添加成员
 */
export async function addMember(
  id: string,
  userIds: Array<string>,
  mode: ErrorMessageMode = 'modal',
) {
  return defHttp.put<boolean>(
    {
      url: Api.AddMember,
      data: { id, userIds },
    },
    {
      errorMessageMode: mode,
    },
  );
}
/**
 * @description: 查询签章成员
 */

export async function getStampMember(id, mode: ErrorMessageMode = 'modal') {
  return defHttp.get(
    {
      url: Api.GetMember,
      params: { id },
    },
    {
      errorMessageMode: mode,
    },
  );
}
