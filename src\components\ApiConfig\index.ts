import { withInstall } from '/@/utils/index';
import apiConfig from './src/ApiConfig.vue';
import selectApiConfig from './src/components/ApiSelect.vue';
import selectInterfaceAddress from './src/components/SelectInterfaceAddress.vue';
import inputModel from './src/components/InputModel.vue';
export const ApiConfig = withInstall(apiConfig);
export const SelectApiConfig = withInstall(selectApiConfig);
export const InputModel = withInstall(inputModel);
export const SelectInterfaceAddress = withInstall(selectInterfaceAddress);
