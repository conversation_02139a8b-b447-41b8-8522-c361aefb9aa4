<template>
  <div :class="hasSlot ? 'rule-box' : ''">
    <slot></slot>
    <template v-if="hasSlot">
      <span v-if="!value" class="rule-color">{{ placeholder }}</span>
    </template>
    <template v-else>
      <Input class="rule-box" :value="value" :placeholder="placeholder" readonly>
        <template #suffix>
          <Icon icon="ant-design:ellipsis-outlined" class="rule-color" />
        </template>
      </Input>
    </template>
    <Icon icon="ant-design:ellipsis-outlined" class="rule-color" v-if="hasSlot" />
  </div>
</template>

<script setup lang="ts">
  import { computed, useSlots } from 'vue';
  import Icon from '/@/components/Icon/index';
  import { Input } from 'ant-design-vue';

  defineProps({
    value: [String, Number],
    placeholder: String,
  });
  const hasSlot = computed(() => {
    return !!useSlots().default;
  });
</script>

<style lang="less" scoped>
  .rule-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 30px;
    border: 1px solid #d9d9d9;
    padding: 0 4px;

    .rule-color {
      color: #d0cfd0;
    }
  }
</style>
