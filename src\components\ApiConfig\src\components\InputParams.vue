<template>
  <a-tabs>
    <a-tab-pane :key="item.key" :tab="item.title" v-for="item in apiParams">
      <a-table
        :dataSource="item.tableInfo"
        :columns="apiConfigColumns"
        :pagination="false"
        :scroll="{ y: '400px' }"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'assignmentType'">
            <a-select
              v-model:value="record.assignmentType"
              style="width: 100%"
              :placeholder="t('请选择赋值类型')"
              allowClear
            >
              <a-select-option
                :value="bind.value"
                v-for="bind in props.paramTree.length > 0 ? processBindType : bindType"
                :key="bind.value"
              >
                {{ bind.label }}
              </a-select-option>
            </a-select>
          </template>
          <template v-else-if="column.key === 'value'">
            <a-tree-select
              v-if="record.assignmentType === 'data'"
              v-model:value="record.config"
              show-search
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              placeholder="Please select"
              allow-clear
              tree-default-expand-all
              :tree-data="paramTree"
              :field-names="{
                children: 'children',
                label: 'title',
                value: 'key',
              }"
            />
            <!-- 表单数据 -->
            <a-tree-select
              v-else-if="record.assignmentType === 'formData'"
              v-model:value="record.config"
              show-search
              style="width: 100%"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              :placeholder="t('请选择表单数据')"
              allow-clear
              tree-default-expand-all
              :tree-data="formDataTree"
              :field-names="{
                children: 'children',
                label: 'title',
                value: 'key',
              }"
            />
            <a-input
              v-else
              v-model:value="record.value"
              :placeholder="record.type ? t('请填写值') : t('请先选择赋值类型后再配置值')"
            />
          </template>
        </template>
      </a-table>
    </a-tab-pane>
  </a-tabs>
</template>

<script setup lang="ts">
  import { TreeProps } from 'ant-design-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { ApiParams } from '../interface';
  import { getVariablesTree } from '/@/views/workflow/design/bpmn/config/info';
  import { ref } from 'vue';
  const { t } = useI18n();
  let props = withDefaults(
    defineProps<{
      paramTree: TreeProps['treeData'];
      apiParams: Array<ApiParams>;
    }>(),
    {
      paramTree: () => {
        return [];
      },
      apiParams: () => {
        return [];
      },
    },
  );

  let bindType = [
    {
      label: t('值'),
      value: 'value',
    },
  ];
  let processBindType = [
    {
      label: t('值'),
      value: 'value',
    },
    {
      label: t('流程数据'),
      value: 'data',
    },
    {
      label: t('流程参数'),
      value: 'processParameter',
    },
    {
      label: t('发起人信息'),
      value: 'originator',
    },
    {
      label: t('表单数据'),
      value: 'formData',
    },
  ];
  let apiConfigColumns = [
    {
      title: t('API入参名称'),
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: t('API入参类型'),
      dataIndex: 'dataType',
      key: 'dataType',
      align: 'center',
    },
    {
      title: t('赋值类型'),
      dataIndex: 'assignmentType',
      key: 'assignmentType',
      align: 'center',
    },
    {
      title: t('赋值配置'),
      dataIndex: 'value',
      key: 'value',
      align: 'center',
    },
  ];
  const formDataTree = ref(getVariablesTree({ needHideComponents: true }));
</script>

<style scoped></style>
