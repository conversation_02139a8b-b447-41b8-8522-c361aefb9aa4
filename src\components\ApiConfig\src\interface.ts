export interface InterfaceList {
  id: string;
  name: string;
  path: string;
}
export interface ApiConfig {
  id: string; //接口Id
  name: string; //接口path
  method: string; //接口请求方式
  path?: string; //接口地址
  script?: string; //接口地址
  requestParamsConfigs: Array<InputParamItem>; //Query Params 输入参数
  requestHeaderConfigs: Array<InputParamItem>; //Header 输入参数
  requestBodyConfigs: Array<InputParamItem>; //Body 输入参数
}
// 参数配置
export interface InputParamItem {
  name: string; //API入参名称
  dataType: string; //API入参类型
  assignmentType: string; //赋值类型
  value: string; //值
  config: string; //赋值配置
}
export interface ApiParams {
  key: string;
  title: string;
  tableInfo: Array<InputParamItem>;
}
