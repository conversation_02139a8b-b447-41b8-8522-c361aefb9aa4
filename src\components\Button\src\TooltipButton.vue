<template>
  <Popover placement="topLeft" trigger="click">
    <template #content>
      <div class="ant-popover-message"
        ><span role="img" aria-label="exclamation-circle" class="anticon anticon-exclamation-circle"
          ><svg
            focusable="false"
            class=""
            data-icon="exclamation-circle"
            width="1em"
            height="1em"
            fill="currentColor"
            aria-hidden="true"
            viewBox="64 64 896 896"
          >
            <path
              d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
            /></svg
        ></span>
        <div class="ant-popover-message-title">{{ t('是否确认删除？') }}</div></div
      >
      <a-button class="mr-2" size="small" @click="emit('cancel')">{{ t('取消') }}</a-button>
      <a-button type="primary" class="mr-2" size="small" @click="emit('confirm')">{{
        t('仅删除发布数据')
      }}</a-button>
      <a-button type="primary" size="small" @click="emit('deletemenu')">{{
        t('删除发布数据和菜单')
      }}</a-button>
    </template>

    <BasicButton :color="action.color" :type="action.type" :size="action.size"
      ><Icon :icon="action.icon" />
    </BasicButton>
  </Popover>
</template>
<script lang="ts" setup>
  import { computed, unref } from 'vue';
  import BasicButton from './BasicButton.vue';
  import { Popover } from 'ant-design-vue';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { useI18n } from '/@/hooks/web/useI18n';
  import Icon from '/@/components/Icon/index';

  const emit = defineEmits(['cancel', 'confirm', 'deletemenu']);
  const { t } = useI18n();
  const attrs = useAttrs();
  const action: any = computed(() => {
    return { ...unref(attrs) };
  });
</script>
