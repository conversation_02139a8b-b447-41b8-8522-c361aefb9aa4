<template>
  <div class="add-box">
    <svg class="icon add-icon" aria-hidden="true">
      <use xlink:href="#icon-add" />
    </svg>
    <div class="dashed-arrow add-arrow" v-if="isLast"></div>
    <div class="bottom-arrow" v-else>
      <div class="top-arrow" :style="{ left: `-${lineHeight}`, width: lineHeight }"> </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
  const props = defineProps({
    lineHeight: String,
    isLast: {
      type: Boolean,
      default: false,
    },
  });
</script>
<style lang="less" scoped>
  .add-box {
    position: relative;
    left: 40px;
    width: 80px;
    height: 80px;
    margin-top: 65px;
    background: #e7faf3;
    display: flex;
    justify-content: center;
    align-items: center;
    transform: rotate(45deg);
    cursor: pointer;

    &::after {
      content: '';
      background: #fff;
      height: 10px;
      width: 10px;
      position: absolute;
      top: v-bind("props.isLast ? 'calc(100% - 10px)' : '0'");
      left: calc(100% - 10px);
      border: 2px solid #00d37e;
      border-radius: 50%;
    }

    .dashed-arrow {
      height: 40px;
      position: absolute;
      bottom: -40px;
      left: calc(50% - 1px);
      border-left: 2px dashed #d9d9d9;

      &::after {
        content: '';
        position: absolute;
        left: -7px;
        bottom: -14px;
        height: 0;
        width: 0;
        border: 6px solid #000;
        border-color: #d9d9d9 transparent transparent;
      }
    }

    .add-icon {
      font-size: 30px;
      background-color: #fff;
      padding: 5px;
      border-radius: 50%;
      transform: rotate(45deg);
      fill: #00d37e;
    }

    .bottom-arrow {
      height: 58px;
      width: 45px;
      position: absolute;
      left: 64px;
      top: -64px;
      transform: rotate(45deg);
      border-top: 2px dashed #d9d9d9;
      border-right: 2px dashed #d9d9d9;
      border-radius: 0 10%;

      .top-arrow {
        height: 48px;
        position: absolute;
        top: -48px;
        border-bottom: 2px dashed #d9d9d9;
        border-left: 2px dashed #d9d9d9;
        border-radius: 0 10%;

        &::after {
          content: '';
          position: absolute;
          left: -7px;
          top: -14px;
          height: 0;
          width: 0;
          border: 6px solid #000;
          border-color: transparent transparent #d9d9d9;
        }
      }
    }

    .add-arrow {
      left: 116%;
      transform: rotate(-45deg);
      top: 92%;
    }
  }

  .icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentcolor;
    overflow: hidden;
  }
</style>
