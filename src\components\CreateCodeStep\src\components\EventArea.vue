<template>
  <div v-for="(item, idx) in columnList" :key="idx" @click="eventClick(idx)">
    <div
      :style="{
        '--bgcColor': item.bgcColor,
        '--color': item.color,
        marginTop: item.isLast ? '68px' : 0,
      }"
      :class="['circle-box', { dot: !item.isLast }]"
      v-if="item.type === 'circle'"
    >
      <svg class="icon circle-icon" aria-hidden="true">
        <use :xlink:href="item.icon" />
      </svg>
      <div class="dashed-arrow" v-if="!item.isLast"> </div>
    </div>
    <div
      :style="{ background: item.bgcColor, '--color': item.color }"
      :class="[{ 'area-outline': item.isClick }, 'area-box']"
      v-else
    >
      <svg class="icon svg-icon" aria-hidden="true">
        <use :xlink:href="item.icon" />
      </svg>
      <p>{{ item.text }}</p>
      <p v-if="item.detail" style="font-size: 12px">{{ item.detail }}</p>
      <svg
        class="icon del-icon"
        aria-hidden="true"
        v-if="item.isUserDefined"
        @click.stop="delEvent(idx)"
      >
        <use xlink:href="#icon-shanchu" />
      </svg>
      <div class="dashed-arrow"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { FormEventStyleConfig } from '../../../../model/generator/formEventConfig';

  const props = defineProps({
    index: Number,
    isLast: {
      type: Boolean,
      default: false,
    },
    columnList: {
      type: Array as PropType<FormEventStyleConfig[]>,
    },
  });

  const emits = defineEmits(['deleteEvent', 'clickNode']);
  const eventClick = (index) => {
    const clickNode = {
      index,
      columnIndex: props.index,
    };
    emits('clickNode', clickNode);
  };
  const delEvent = (index) => {
    const clickNode = {
      index,
      columnIndex: props.index,
    };
    emits('deleteEvent', clickNode);
  };
</script>

<style lang="less" scoped>
  .circle-box {
    position: relative;
    left: 50px;
    width: 60px;
    height: 60px;
    display: flex;
    justify-content: center;
    align-items: center;
    background: var(--bgcColor);
    margin-bottom: 54px;
    border-radius: 50%;
    cursor: pointer;

    &.dot::after {
      content: '';
      background: #fff;
      height: 10px;
      width: 10px;
      position: absolute;
      bottom: -5px;
      left: calc(50% - 5px);
      border: 2px solid var(--color);
      border-radius: 50%;
    }

    .circle-icon {
      font-size: 32px;
      fill: var(--color);
    }
  }

  .area-box {
    position: relative;
    width: 135px;
    height: 90px;
    padding: 10px 0 0 15px;
    // margin-bottom: 65px;
    margin-top: 48px;
    border-radius: 6px 6px 6px 6px;
    border: 5px solid #fff;
    box-sizing: content-box;
    cursor: pointer;
    // background: v-bind(bgcColor);

    &::after {
      content: '';
      background: #fff;
      height: 10px;
      width: 10px;
      position: absolute;
      bottom: -5px;
      left: calc(50% - 5px);
      border: 2px solid var(--color);
      border-radius: 50%;
    }

    .svg-icon {
      font-size: 22px;
      background: #fff;
      padding: 8px;
      margin-bottom: 8px;
      box-sizing: content-box;
      border-radius: 50%;
      fill: var(--color);
    }

    p {
      margin-bottom: 0;
    }

    .del-icon {
      position: absolute;
      right: 10px;
      font-size: 14px;
      fill: #2774ff;
    }
  }

  .area-outline {
    outline: 2px dashed #2774ff !important;
  }

  .dashed-arrow {
    height: 40px;
    position: absolute;
    bottom: -40px;
    left: calc(50% - 1px);
    border-left: 2px dashed #d9d9d9;

    &::after {
      content: '';
      position: absolute;
      left: -7px;
      bottom: -14px;
      height: 0px;
      width: 0px;
      border: 6px solid #000;
      border-top-color: #d9d9d9;
      border-bottom-color: transparent;
      border-left-color: transparent;
      border-right-color: transparent;
    }
  }

  .icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
  }
</style>
