<template>
  <BasicModal
    wrap-class-name="fixedHeight"
    @register="registerModal"
    :title="t('选择数据库')"
    v-bind="$attrs"
    width="800px"
    :fixedHeight="true"
    @ok="handlerClick"
  >
    <BasicTable @register="registerTable" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useTable, FormSchema, BasicColumn, BasicTable } from '/@/components/Table';
  import { getDatabaselinkTable } from '/@/api/system/databaselink';
  import { ref } from 'vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { TableConfig } from '/@/model/generator/tableConfig';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const searchFormSchema: FormSchema[] = [
    {
      field: 'tableName',
      label: t('表名'),
      component: 'Input',
    },
  ];

  const columns: BasicColumn[] = [
    {
      title: t('表名'),
      dataIndex: 'tableName',
      width: 200,
    },
    {
      title: t('备注'),
      dataIndex: 'tableComment',
      width: 200,
    },
  ];

  const databaseId = ref('');
  const selectedKeys = ref<string[] | number[]>([]);

  const emit = defineEmits(['success', 'register']);

  const { createMessage } = useMessage();
  const selectTableName = ref<string[]>([]);
  const formConfig = {
    rowProps: {
      gutter: 16,
    },
    schemas: searchFormSchema,
    showResetButton: false,
  };
  const [registerTable, { getSelectRows, setSelectedRowKeys, reload }] = useTable({
    title: t('数据表列表'),
    api: getDatabaselinkTable,
    striped: false,
    rowKey: 'tableName',
    columns,
    formConfig,
    beforeFetch: (params) => {
      //发送请求默认新增  左边树结构所选机构id
      return { ...params, id: databaseId.value };
    },
    afterFetch: () => {
      //搜索完成后 将选中项重新赋值给SelectedRowKeys
      setSelectedRowKeys(selectedKeys.value);
    },
    rowSelection: {
      type: 'checkbox',
      getCheckboxProps: (record) => ({
        disabled: selectTableName.value.includes(record.tableName),
      }),
      onChange: (selectedRowKeys: string[]) => {
        //搜索后会把原有选中项清空 所以需要进行存储
        selectedKeys.value = selectedRowKeys;
      },
    },
    isPaginateByDataSource: true,
    useSearchForm: true,
    showTableSetting: true,
    tableSetting: {
      size: false,
    },
  });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    selectTableName.value = data.selectTableName;
    setModalProps({ confirmLoading: false, canFullscreen: false });
    setSelectedRowKeys(data.selectTableName);
    databaseId.value = data.databaseId;

    reload({ searchInfo: { id: databaseId.value } });
  });

  const handlerClick = () => {
    const selectRows = getSelectRows() as TableConfig[];
    if (selectRows.length === 0) {
      createMessage.error(t('至少需要选择一个数据表！'));
      return;
    }
    if (selectTableName.value.length === 0) {
      selectRows.map((item, index) => {
        item.order = index + 1;
        if (index === 0) {
          item.isMain = true;
        } else {
          item.isMain = false;
          item.isSubForm = true;
        }
      });
    }
    emit('success', selectRows);
    closeModal();
  };
</script>
<style scoped>
  :deep(.ant-pagination) {
    margin-top: 15px;
  }

  :deep(.ant-table-wrapper .ant-table-title) {
    padding-bottom: 0 !important;
  }

  :deep(.ant-table) {
    height: calc(100% - 44px);
  }

  :deep(.ant-table-container) {
    height: calc(100% - 102px);
  }

  :deep(.ant-modal) {
    top: 20px;
  }

  :deep(.ant-table-wrapper) {
    padding-top: 0;
    padding-bottom: 0;
  }
</style>
