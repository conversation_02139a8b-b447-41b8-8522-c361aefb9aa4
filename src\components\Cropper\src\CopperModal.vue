<template>
  <BasicModal
    v-bind="$attrs"
    @register="register"
    :title="t('头像上传')"
    width="800px"
    :canFullscreen="false"
    @ok="handleOk"
    :okText="t('确认')"
  >
    <div :class="prefixCls">
      <div :class="`${prefixCls}-left`">
        <div :class="`${prefixCls}-cropper`">
          <CropperImage
            v-if="src"
            :src="src"
            height="300px"
            :circled="circled"
            @cropend="handleCropend"
            @ready="handleReady"
          />
        </div>

        <div :class="`${prefixCls}-toolbar`">
          <Upload :fileList="[]" accept="image/*" :beforeUpload="handleBeforeUpload">
            <Tooltip :title="t('选择图片')" placement="bottom">
              <a-button size="small" type="primary">
                <template #icon><Icon icon="ant-design:upload-outlined" /></template>
              </a-button>
            </Tooltip>
          </Upload>
          <Space>
            <Tooltip :title="t('重置')" placement="bottom">
              <a-button
                type="primary"
                size="small"
                :disabled="!src"
                @click="handlerToolbar('reset')"
              >
                <template #icon><Icon icon="ant-design:reload-outlined" /></template>
              </a-button>
            </Tooltip>
            <Tooltip :title="t('逆时针旋转')" placement="bottom">
              <a-button
                type="primary"
                size="small"
                :disabled="!src"
                @click="handlerToolbar('rotate', -45)"
              >
                <template #icon><Icon icon="ant-design:rotate-left-outlined" /></template>
              </a-button>
            </Tooltip>
            <Tooltip :title="t('顺时针旋转')" placement="bottom">
              <a-button
                type="primary"
                size="small"
                :disabled="!src"
                @click="handlerToolbar('rotate', 45)"
              >
                <template #icon><Icon icon="ant-design:rotate-right-outlined" /></template>
              </a-button>
            </Tooltip>
            <Tooltip :title="t('水平翻转')" placement="bottom">
              <a-button
                type="primary"
                size="small"
                :disabled="!src"
                @click="handlerToolbar('scaleX')"
              >
                <template #icon><Icon icon="vaadin:arrows-long-h" /></template>
              </a-button>
            </Tooltip>
            <Tooltip :title="t('垂直翻转')" placement="bottom">
              <a-button
                type="primary"
                size="small"
                :disabled="!src"
                @click="handlerToolbar('scaleY')"
              >
                <template #icon><Icon icon="vaadin:arrows-long-v" /></template>
              </a-button>
            </Tooltip>
            <Tooltip :title="t('放大')" placement="bottom">
              <a-button
                type="primary"
                size="small"
                :disabled="!src"
                @click="handlerToolbar('zoom', 0.1)"
              >
                <template #icon><Icon icon="ant-design:zoom-in-outlined" /></template>
              </a-button>
            </Tooltip>
            <Tooltip :title="t('缩小')" placement="bottom">
              <a-button
                type="primary"
                size="small"
                :disabled="!src"
                @click="handlerToolbar('zoom', -0.1)"
              >
                <template #icon><Icon icon="ant-design:zoom-out-outlined" /></template>
              </a-button>
            </Tooltip>
          </Space>
        </div>
      </div>
      <div :class="`${prefixCls}-right`">
        <div :class="`${prefixCls}-preview`">
          <img :src="previewSource" v-if="previewSource" :alt="t('预览')" />
        </div>
        <template v-if="previewSource">
          <div :class="`${prefixCls}-group`">
            <Avatar :src="previewSource" size="large" />
            <Avatar :src="previewSource" :size="48" />
            <Avatar :src="previewSource" :size="64" />
            <Avatar :src="previewSource" :size="80" />
          </div>
        </template>
      </div>
    </div>
  </BasicModal>
</template>
<script lang="ts">
  import type { CropendResult, Cropper } from './typing';
  import { Icon } from '/@/components/Icon';
  import { defineComponent, ref } from 'vue';
  import CropperImage from './Cropper.vue';
  import { Space, Upload, Avatar, Tooltip } from 'ant-design-vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { dataURLtoBlob } from '/@/utils/file/base64Conver';
  import { isFunction } from '/@/utils/is';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';

  type apiFunParams = { file: Blob; name: string; filename: string };

  const props = {
    circled: { type: Boolean, default: true },
    uploadApi: {
      type: Function as PropType<(params: apiFunParams) => Promise<any>>,
    },
    maxSize: { type: Number },
  };

  export default defineComponent({
    name: 'CropperModal',
    components: { BasicModal, Space, CropperImage, Upload, Avatar, Tooltip, Icon },
    props,
    emits: ['uploadSuccess', 'register'],
    setup(props, { emit }) {
      let filename = '';
      const src = ref('');
      const previewSource = ref('');
      const cropper = ref<Cropper>();
      let scaleX = 1;
      let scaleY = 1;

      const { prefixCls } = useDesign('cropper-am');
      const [register, { closeModal, setModalProps }] = useModalInner();
      const { t } = useI18n();
      const { notification } = useMessage();

      // Block upload
      function handleBeforeUpload(file: File) {
        if (props.maxSize && file.size / 1024 / 1024 > props.maxSize) {
          notification.error({
            message: 'Tip',
            description: `图片大小不能超过${props.maxSize}MB！`,
          });
          return false;
        }
        const reader = new FileReader();
        reader.readAsDataURL(file);
        src.value = '';
        previewSource.value = '';
        reader.onload = function (e) {
          src.value = (e.target?.result as string) ?? '';
          filename = file.name;
        };
        return false;
      }

      function handleCropend({ imgBase64 }: CropendResult) {
        previewSource.value = imgBase64;
      }

      function handleReady(cropperInstance: Cropper) {
        cropper.value = cropperInstance;
      }

      function handlerToolbar(event: string, arg?: number) {
        if (event === 'scaleX') {
          scaleX = arg = scaleX === -1 ? 1 : -1;
        }
        if (event === 'scaleY') {
          scaleY = arg = scaleY === -1 ? 1 : -1;
        }
        cropper?.value?.[event]?.(arg);
      }

      async function handleOk() {
        const uploadApi = props.uploadApi;
        if (uploadApi && isFunction(uploadApi)) {
          const blob = dataURLtoBlob(previewSource.value);
          try {
            setModalProps({ confirmLoading: true });
            const result = await uploadApi({ name: 'file', file: blob, filename });
            emit('uploadSuccess', { source: previewSource.value, data: result.data });
            closeModal();
          } finally {
            setModalProps({ confirmLoading: false });
          }
        }
      }

      return {
        t,
        prefixCls,
        src,
        register,
        previewSource,
        handleBeforeUpload,
        handleCropend,
        handleReady,
        handlerToolbar,
        handleOk,
      };
    },
  });
</script>

<style lang="less">
  @prefix-cls: ~'@{namespace}-cropper-am';

  .@{prefix-cls} {
    display: flex;

    &-left,
    &-right {
      height: 340px;
    }

    &-left {
      width: 55%;
    }

    &-right {
      width: 45%;
    }

    &-cropper {
      height: 300px;
      background: #eee;
      background-image: linear-gradient(
          45deg,
          rgb(0 0 0 / 25%) 25%,
          transparent 0,
          transparent 75%,
          rgb(0 0 0 / 25%) 0
        ),
        linear-gradient(
          45deg,
          rgb(0 0 0 / 25%) 25%,
          transparent 0,
          transparent 75%,
          rgb(0 0 0 / 25%) 0
        );
      background-position: 0 0, 12px 12px;
      background-size: 24px 24px;
    }

    &-toolbar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 10px;
    }

    &-preview {
      width: 220px;
      height: 220px;
      margin: 0 auto;
      overflow: hidden;
      border: 1px solid @border-color-base;
      border-radius: 50%;

      img {
        width: 100%;
        height: 100%;
      }
    }

    &-group {
      display: flex;
      padding-top: 8px;
      margin-top: 8px;
      border-top: 1px solid @border-color-base;
      justify-content: space-around;
      align-items: center;
    }
  }
</style>
