<template>
  <a-date-picker
    :size="size"
    v-model:value="modelValue"
    :placeholder="placeholder"
    :format="format"
    :valueFormat="format"
    :allowClear="allowClear"
    :disabled="disabled"
    :showTime="format === 'YYYY-MM-DD HH:mm:ss'"
    :picker="picker"
    @change="handleChange"
  />
</template>
<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { ref, watch, computed } from 'vue';

  const props = defineProps({
    value: [dayjs, String, Object],
    size: String,
    placeholder: String,
    format: String,
    allowClear: <PERSON>olean,
    disabled: <PERSON><PERSON><PERSON>,
  });
  const modelValue = ref<string>();
  const emit = defineEmits(['update:value']);

  const picker = computed(() => {
    let time = 'date';
    switch (props.format) {
      case 'YYYY-MM':
        time = 'month';
        break;
      case 'YYYY':
        time = 'year';
        break;
    }
    return time;
  });
  watch(
    () => props.value,
    (val) => {
      if (val && typeof val !== 'string') {
        modelValue.value = val?.format(props.format);
      } else {
        modelValue.value = val || '';
      }
    },
    {
      immediate: true,
    },
  );
  const handleChange = (time) => {
    emit('update:value', time);
  };
</script>
