<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    showFooter
    title="新增数据库链接"
    width="500px"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
    <template #insertFooter>
      <a-button type="primary" danger @click="handleTest"> 测试链接 </a-button>
    </template>
  </BasicDrawer>
</template>
<script lang="tsx" setup>
  import { ref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { FormSchema } from '/@/components/Table';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { addDatabaseLink, testDatabaseLink } from '/@/api/system/databaselink';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';

  const schema: FormSchema[] = [
    {
      field: 'dbName',
      label: '数据库名称',
      component: 'Input',
      required: true,
      colProps: { span: 24 },
    },
    {
      field: 'dbType',
      label: '数据库类型',
      component: 'Select',
      required: true,
      componentProps: {
        options: [
          { label: 'mysql', value: 'mysql' },
          { label: 'sqlserver', value: 'sqlserver' },
          { label: 'oracle', value: 'oracle' },
          { label: 'pgsql', value: 'pgsql' },
          { label: 'dm', value: 'dm' },
          { label: 'mariadb', value: 'mariadb' },
          { label: 'sqlite', value: 'sqlite' },
          { label: 'gbase', value: 'gbase' },
          { label: 'oceanbase', value: 'oceanbase' },
          { label: 'h2', value: 'h2' },
          { label: 'clickhouse', value: 'clickhouse' },
        ],
      },
      colProps: { span: 12 },
    },
    {
      field: 'dbVersion',
      label: '数据库版本',
      component: 'Input',
      colProps: { span: 24 },
    },
    // {
    //   field: 'driver',
    //   label: '驱动',
    //   component: 'Input',
    //   required: true,
    //   colProps: { span: 24 },
    // },
    {
      field: 'host',
      label: '链接',
      required: true,
      component: 'InputTextArea',
      colProps: { span: 24 },
    },
    {
      field: 'username',
      label: '账号',
      component: 'Input',
      required: true,
      colProps: { span: 24 },
    },
    {
      field: 'password',
      label: '密码',
      component: 'InputPassword',
      required: true,
      colProps: { span: 24 },
    },
  ];

  const emit = defineEmits(['success', 'register']);

  const { notification } = useMessage();
  const loading = ref(false);

  const [registerForm, { resetFields, validate }] = useForm({
    labelWidth: 100,
    schemas: schema,
    showActionButtonGroup: false,
    actionColOptions: {
      span: 23,
    },
  });

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async () => {
    resetFields();
    setDrawerProps({ confirmLoading: false });
  });

  const handleTest = async () => {
    const values = await validate();
    console.log('handleTest');

    const ok = await testDatabaseLink(values);
    loading.value = true;
    if (!ok) {
      notification.error({
        message: 'Tip',
        type: 'warning',
        description: '未能连接上数据库',
      }); //提示消息
    } else {
      notification.success({
        message: 'Tip',
        description: '成功',
      }); //提示消息
    }
    loading.value = false;
  };

  async function handleSubmit() {
    try {
      const values = await validate();

      setDrawerProps({ confirmLoading: true });
      await addDatabaseLink(values);
      notification.success({
        message: 'Tip',
        description: '数据库连接新增成功！',
      }); //提示消息
      closeDrawer();
      emit('success');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
