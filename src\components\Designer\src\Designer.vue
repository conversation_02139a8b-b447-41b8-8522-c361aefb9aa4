<template>
  <div class="fc-style">
    <a-layout class="fc-container">
      <a-layout-content class="fc-main">
        <a-layout>
          <a-layout-sider theme="light" :width="250" class="cg-container">
            <div class="components">
              <a-collapse
                v-model:activeKey="componentGroupKey"
                :bordered="false"
                expand-icon-position="right"
                ghost
              >
                <template #expandIcon="{ isActive }">
                  <double-right-outlined :rotate="isActive ? -90 : 90" style="color: #ccc" />
                </template>
                <a-collapse-panel
                  v-for="(item, index) in componentList"
                  :key="index + 1"
                  :header="item.title"
                >
                  <ComponentGroup :fields="item.fields" :list="item.list" :isDisabled="!dbValue" />
                </a-collapse-panel>
              </a-collapse>
            </div>
          </a-layout-sider>
          <a-layout class="center-container">
            <AntdHeader
              v-bind="$props"
              @preview="handlerPreview"
              @upload-json="uploadJsonVisible = true"
              @generate-json="handleGenerateJson"
              @generate-code="handleGenerateCode"
              @clearable="handleClearable"
            >
              <slot name="header">
                <a-row class="float-left text-left w-[240px]" align="middle" v-if="isShowSelectDb">
                  <a-col :span="8" align="right">
                    <span style="color: red">*</span>
                    数据库：
                  </a-col>
                  <a-col :span="16">
                    <DbSelect
                      :placeholder="t('请选择数据库')"
                      @change="handleDbChange"
                      :disabled="isDbDisabled"
                      v-model:value="dbValue"
                    />
                  </a-col>
                </a-row>
              </slot>
            </AntdHeader>
            <a-layout-content id="layoutId" :class="{ 'widget-empty': widgetForm.list }">
              <AntdWidgetForm
                ref="widgetFormRef"
                v-model:widgetForm="widgetForm"
                v-model:widgetFormSelect="widgetFormSelect"
              />
            </a-layout-content>
          </a-layout>
          <a-layout-sider theme="light" class="widget-config-container" :width="320">
            <a-layout class="layout-height">
              <!-- <a-layout-header class="right-tab"> -->
              <a-tabs
                v-model:activeKey="configTab"
                centered
                style="margin-bottom: 8px; background: #fff; height: 100%"
              >
                <a-tab-pane key="widget" :tab="t('组件属性')">
                  <a-collapse
                    v-model:activeKey="chooseTab"
                    ghost
                    expandIconPosition="right"
                    v-if="widgetFormSelect"
                    style="margin: 5px 0 10px 10px"
                  >
                    <a-collapse-panel key="property" :header="t('属性设置')">
                      <TableLayoutOption
                        v-if="widgetFormSelect.type == 'table-layout'"
                        v-model:select="widgetFormSelect"
                        :widgetForm="widgetForm"
                      ></TableLayoutOption>
                      <PropertyOption
                        v-else
                        v-model:select="widgetFormSelect"
                        :widgetForm="widgetForm"
                      />
                    </a-collapse-panel>
                    <a-collapse-panel
                      key="list"
                      :header="t('列表配置')"
                      v-if="hasListConfigsComponents(widgetFormSelect.type)"
                    >
                      <ComponentListConfigs
                        v-model:select="widgetFormSelect"
                        :widgetForm="widgetForm"
                      />
                    </a-collapse-panel>
                    <a-collapse-panel key="event" :header="t('触发事件')">
                      <div
                        class="widget-none"
                        v-if="noConfigComponentEvent.includes(widgetFormSelect.type)"
                      >
                        <SvgIcon name="exclamation" :size="44" />
                        <p>{{ t('当前组件无法配置触发组件') }}</p>
                      </div>
                      <ComponentEvent
                        v-model:select="widgetFormSelect"
                        :widgetForm="widgetForm"
                        v-else
                      />
                    </a-collapse-panel>
                    <a-collapse-panel key="regular" :header="t('正则校验')">
                      <div
                        class="widget-none"
                        v-if="noConfigRegularSetting.includes(widgetFormSelect.type)"
                      >
                        <SvgIcon name="exclamation" :size="44" />
                        <p>{{ t('当前组件无法配置正则检验') }}</p>
                      </div>
                      <RegularSetting v-model:select="widgetFormSelect" v-else />
                    </a-collapse-panel>
                  </a-collapse>
                  <div class="widget-none" v-else>
                    <SvgIcon name="tool" :size="44" />
                    <p>{{ t('请先拖入组件后再查看组件属性') }}</p>
                  </div>
                </a-tab-pane>
                <a-tab-pane key="form" :tab="t('表单属性')">
                  <AntdFormConfig v-model:config="widgetForm.config" />
                </a-tab-pane>
                <a-tab-pane key="hidden" :tab="t('隐藏组件')">
                  <HiddenComponent v-model:component="widgetForm.hiddenComponent" />
                </a-tab-pane>
              </a-tabs>
            </a-layout>
          </a-layout-sider>
        </a-layout>
      </a-layout-content>

      <a-modal
        v-model:visible="uploadJsonVisible"
        :title="t('导入JSON')"
        :width="800"
        @ok="handleUploadJson"
        :okText="t('确认')"
        :cancelText="t('取消')"
      >
        <a-alert
          type="info"
          :message="t('JSON格式如下，直接复制生成的json覆盖此处代码点击确定即可')"
          style="margin-bottom: 10px"
        />
        <CodeEditor v-model:value="jsonEg" language="json" />
      </a-modal>

      <PreviewDrawer @register="registerDrawer" />

      <a-modal
        v-model:visible="generateJsonVisible"
        :title="t('生成JSON')"
        :okText="t('复制')"
        :cancelText="t('取消')"
        :width="800"
        @ok="handleCopyClick(generateJsonTemplate)"
      >
        <CodeEditor :value="generateJsonTemplate" language="json" readonly />
      </a-modal>

      <a-modal
        v-model:visible="dataJsonVisible"
        :title="t('获取数据')"
        :okText="t('复制')"
        :cancelText="t('取消')"
        :width="800"
        @ok="handleCopyClick(dataJsonTemplate)"
      >
        <CodeEditor :value="dataJsonTemplate" language="json" readonly />
      </a-modal>

      <a-modal
        v-model:visible="dataCodeVisible"
        :title="t('生产代码')"
        :okText="t('复制')"
        :cancelText="t('取消')"
        :width="800"
        @ok="handleCopyClick(dataCodeTemplate)"
      >
        <a-tabs type="card" v-model:activeKey="codeLanguage" :tabBarStyle="{ margin: 0 }">
          <a-tab-pane tab="Vue Component" :key="codeType.Vue">
            <CodeEditor :value="dataCodeTemplate" language="html" readonly />
          </a-tab-pane>
          <a-tab-pane tab="HTML" :key="codeType.Html">
            <CodeEditor :value="dataCodeTemplate" language="html" readonly />
          </a-tab-pane>
        </a-tabs>
      </a-modal>
    </a-layout>
  </div>
</template>

<script lang="ts">
  import {
    defineComponent,
    reactive,
    PropType,
    toRefs,
    watchEffect,
    provide,
    watch,
    inject,
    ref,
    onMounted,
    Ref,
  } from 'vue';
  import { message } from 'ant-design-vue';
  import { cloneDeep, merge, debounce } from 'lodash-es';
  import { CodeEditor } from '/@/components/CodeEditor';
  import ComponentGroup from './components/ComponentGroup.vue';
  import AntdHeader from './components/AntdHeader.vue';
  import AntdWidgetForm from './components/AntdWidgetForm.vue';
  import PropertyOption from './components/componentProperty/PropertyOption.vue';
  import TableLayoutOption from './components/componentProperty/TableLayoutOption.vue';
  import RegularSetting from './components/componentProperty/RegularSetting.vue';
  import ComponentEvent from './components/componentProperty/ComponentEvent.vue';
  import ComponentListConfigs from './components/componentProperty/ComponentListConfigs.vue'; //列表属性配置
  import AntdFormConfig from './components/AntdFormConfig.vue';
  import HiddenComponent from './components/HiddenComponent.vue';
  import { DbSelect } from '/@/components/DbSelect';
  import * as antd from './types';
  import { copy } from '/@/utils';
  import generateCode from './util/generateCode';
  import {
    WidgetForm,
    CodeType,
    PlatformType,
    upperFieldDb,
    TableCell,
    TableTh,
    listConfigsComponents,
  } from './types';
  import PreviewDrawer from './components/PreviewDrawer.vue';
  import { useDrawer } from '/@/components/Drawer';
  import { GeneratorConfig } from '/@/model/generator/generatorConfig';
  import { noConfigComponentEvent, noConfigRegularSetting, noHaveTableAndField } from './types';
  import { DoubleRightOutlined } from '@ant-design/icons-vue';
  import { SvgIcon } from '/@/components/Icon';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  export default defineComponent({
    name: 'AntdDesignForm',
    components: {
      AntdHeader,
      ComponentGroup,
      CodeEditor,
      AntdWidgetForm,
      PreviewDrawer,
      AntdFormConfig,
      PropertyOption,
      RegularSetting,
      ComponentEvent,
      HiddenComponent,
      DoubleRightOutlined,
      SvgIcon,
      DbSelect,
      TableLayoutOption,
      ComponentListConfigs,
    },
    props: {
      preview: {
        type: Boolean,
        default: true,
      },

      generateJson: {
        type: Boolean,
        default: true,
      },
      uploadJson: {
        type: Boolean,
        default: true,
      },
      clearable: {
        type: Boolean,
        default: true,
      },
      basicFields: {
        type: Array as PropType<Array<string>>,
        default: () => [
          'input',
          'textarea',
          'password',
          'number',
          'richtext-editor',
          'auto-code',
          'auto-complete',
          'signature',
          'labelComponent',
        ],
      },
      advanceFields: {
        type: Array as PropType<Array<string>>,
        default: () => [
          'select',
          'cascader',
          'associate-select',
          'multiple-popup',
          'associate-popup',
          'area',
          'checkbox',
          'radio',
          'switch',
          'slider',
          'time',
          'time-range',
          'date',
          'date-range',
          'rate',
          'picker-color',
          'upload',
          'image',
          'map',
          'qrcode',
          'tree-component',
          'tree-select-component',
          'barcode',
          'file',
        ],
      },
      buttonFields: {
        type: Array as PropType<Array<string>>,
        default: () => ['button'],
      },
      layoutFields: {
        type: Array as PropType<Array<string>>,
        default: () => [
          'form',
          'sun-form',
          'one-for-one',
          'form-view',

          'title',
          'text',
          'grid',

          'tab',

          'card',
          'iframe',

          'divider',
          ,
          'table-layout',
        ],
      },
      workFlowFields: {
        type: Array as PropType<Array<string>>,
        default: () => ['opinion'],
      },
      infoFields: {
        type: Array as PropType<Array<string>>,
        default: () => ['info', 'organization', 'user'],
      },
      financeFields: {
        type: Array as PropType<Array<string>>,
        default: () => ['computational', 'money-chinese'],
      },
    },
    setup(props) {
      const state = reactive({
        antd,
        codeType: CodeType,
        widgetForm: undefined as any,
        widgetFormSelect: undefined as any,
        generateFormRef: null as any,
        configTab: 'widget',
        chooseTab: 'property',
        previewVisible: false,
        uploadJsonVisible: false,
        dataJsonVisible: false,
        dataCodeVisible: false,
        generateJsonVisible: false,
        generateCodeVisible: false,
        generateJsonTemplate: JSON.stringify(antd.widgetForm, null, 2),
        dataJsonTemplate: '',
        dataCodeTemplate: '',
        codeLanguage: CodeType.Vue,
        jsonEg: JSON.stringify(antd.widgetForm, null, 2),
        componentGroupKey: ['1', '2', '3', '4', '5', '6', '7'],
        componentList: [
          {
            title: t('输入型组件'),
            fields: props.basicFields,
            list: antd.basicComponents,
          },
          {
            title: t('选择型组件'),
            fields: props.advanceFields,
            list: antd.advanceComponents,
          },
          {
            title: t('按钮型组件'),
            fields: props.buttonFields,
            list: antd.buttonComponents,
          },
          {
            title: t('布局型组件'),
            fields: props.layoutFields,
            list: antd.layoutComponents,
          },
          {
            title: t('工作流组件'),
            fields: props.workFlowFields,
            list: antd.workFlowComponents,
          },
          {
            title: t('组织架构'),
            fields: props.infoFields,
            list: antd.infoComponents,
          },
          {
            title: t('财务组件'),
            fields: props.financeFields,
            list: antd.financeComponents,
          },
        ],
      });
      state.widgetForm = inject('widgetForm');
      const generatorConfig = inject<GeneratorConfig>('generatorConfig') as GeneratorConfig;
      const designType = inject<string>('designType');
      let isFieldUpper = inject<Ref<boolean>>('isFieldUpper', ref(false));
      let mainTableName = inject<Ref<string>>('mainTableName', ref(''));

      const { notification } = useMessage();

      const isDbDisabled = ref(false);
      const dbValue = ref();
      const isShowSelectDb = ref(false);
      const tableCell = ref<TableCell>();
      const tableTh = ref<TableTh>();
      provide('tableCell', tableCell);
      provide('tableTh', tableTh);

      watch(
        () => generatorConfig.databaseId,
        (val) => {
          if (val) {
            dbValue.value = val;
            if (state.widgetForm?.list?.length) {
              isDbDisabled.value = true;
            }
          }
        },
        {
          immediate: true,
        },
      );

      onMounted(() => {
        isShowSelectDb.value = designType === 'code';
      });

      //注入数据
      provide('state', state);

      // 列表配置
      const hasListConfigsComponents = (type) => {
        return listConfigsComponents.includes(type);
      };
      const handleUploadJson = () => {
        try {
          setJson(JSON.parse(state.jsonEg));
          state.uploadJsonVisible = false;
          message.success(t('上传成功'));
        } catch (error) {
          message.error(t('上传失败'));
        }
      };

      const handleCopyClick = (text: string) => {
        copy(text);
        message.success(t('Copy成功'));
      };

      const handleGetData = () => {
        state.generateFormRef.getData().then((res: any) => {
          state.dataJsonTemplate = JSON.stringify(res, null, 2);
          state.dataJsonVisible = true;
        });
      };

      const handleGenerateJson = () => {
        (state.generateJsonTemplate = JSON.stringify(state.widgetForm, null, 2)) &&
          (state.generateJsonVisible = true);
      };

      const handleGenerateCode = () => {
        state.codeLanguage = CodeType.Vue;
        state.dataCodeVisible = true;
      };

      watchEffect(() => {
        if (state.dataCodeVisible) {
          state.dataCodeTemplate = generateCode(
            state.widgetForm,
            state.codeLanguage,
            PlatformType.Antd,
          )!;
        }
      });

      const handleClearable = () => {
        state.widgetForm.list = [];
        merge(state.widgetForm, JSON.parse(JSON.stringify(antd.widgetForm)));
        state.widgetFormSelect = undefined;
      };

      const handleReset = () => state.generateFormRef.reset();

      const getJson = () => state.widgetForm;

      const setJson = (json: WidgetForm) => {
        state.widgetForm.list = [];
        merge(state.widgetForm, json);
        if (json.list?.length) {
          state.widgetFormSelect = json.list[0];
        }
      };
      const setWidgetFormSelect = (json: WidgetForm) => {
        if (json.list?.length) {
          state.widgetFormSelect = json.list[0];
        }
      };
      const handlerPreview = () => {
        if (designType === 'data' && hasNoBindTableOrField(state.widgetForm.list)) {
          notification.error({
            message: t('提示'),
            description: t('请先将组件绑定表和字段'),
          }); //提示消息
        } else {
          openDrawer(true, getJson());
        }
      };

      const hasNoBindTableOrField = (component) => {
        const hasSubComponent = [
          'tab',
          'grid',
          'card',
          'one-for-one',
          'form',
          'table-layout',
          'sun-form',
        ];
        return component?.some((info) => {
          if (hasSubComponent.includes(info.type!)) {
            if (info.type === 'form' || info.type === 'one-for-one' || info.type === 'sun-form') {
              return hasNoBindTableOrField(info.children);
            } else if (info.type === 'table-layout') {
              return info.layout?.some((childInfo) => {
                return childInfo.list?.some((child) => {
                  return hasNoBindTableOrField(child.children);
                });
              });
            } else {
              return info.layout?.some((childInfo) => {
                return hasNoBindTableOrField(childInfo.list);
              });
            }
          } else {
            if (info.type === 'time-range' || info.type === 'date-range') {
              return !info.bindTable || !info.bindStartTime || !info.bindEndTime;
            }
            if (info.type === 'input' && info.options.isSave) {
              return false;
            }
            return noHaveTableAndField.includes(info.type)
              ? false
              : !info.bindTable || !info.bindField;
          }
        });
      };

      const handleDbChange = (value, option) => {
        generatorConfig.databaseId = value;
        if (option) isFieldUpper.value = upperFieldDb.includes(option.dbType);
        mainTableName.value = isFieldUpper.value
          ? mainTableName.value.toUpperCase()
          : mainTableName.value;
      };

      const getTemplate = (codeType: CodeType) =>
        generateCode(state.widgetForm, codeType, PlatformType.Antd);

      const clear = () => handleClearable();

      //这里使用了防抖 包裹  因为监听 state.widgetForm 会执行2次
      watch(
        () => state.widgetForm,
        debounce((val) => {
          isDbDisabled.value = !!val?.list?.length || !!val?.hiddenComponent?.length;
          generatorConfig.formJson = cloneDeep(val);
        }, 50),
        { deep: true },
      );

      //注册抽屉 获取外部操作抽屉得方法
      const [registerDrawer, { openDrawer }] = useDrawer();

      return {
        ...toRefs(state),
        handleUploadJson,
        handleCopyClick,
        handleGetData,
        handleGenerateJson,
        handleGenerateCode,
        handleClearable,
        handleReset,
        getJson,
        setJson,
        setWidgetFormSelect,
        getTemplate,
        clear,
        noConfigComponentEvent,
        noConfigRegularSetting,
        handlerPreview,
        registerDrawer,
        t,
        handleDbChange,
        isDbDisabled,
        isShowSelectDb,
        dbValue,
        tableCell,
        tableTh,
        hasListConfigsComponents,
      };
    },
  });
</script>
<style scoped lang="less">
  .fc-style {
    height: 100%;
  }

  .layout-height {
    height: 100%;
    background-color: #f0f2f5;
  }

  :deep(.ant-collapse-header) {
    padding-left: 10px !important;
    margin: 10px 0;
    height: 24px;
    align-items: center !important;
    border-left: 6px solid #5e95ff;
  }

  :deep(.ant-collapse-content-box) {
    padding: 0 0 5px;
  }

  :deep(.ant-tabs) {
    height: 100%;
  }

  :deep(.ant-tabs-content-holder) {
    overflow-y: auto;
    padding: 0 10px 10px 0;
  }

  :deep(.ant-tabs-content-top) {
    height: 100%;
  }

  :deep(.ant-radio-group) {
    display: flex;

    .ant-radio-button-wrapper {
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 1;
      padding: 3px 0 !important;
    }
  }

  .widget-none {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    p {
      margin: 10px;
      color: #cecece;
    }
  }

  :deep(.ant-tabs-nav) {
    margin-bottom: 8px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 10px;
  }

  :deep(.ant-radio-group-small .ant-radio-button-wrapper) {
    padding: 3px 10px;
    box-sizing: content-box;
  }

  .cg-container {
    margin-bottom: 8px;
    overflow-y: scroll;
    padding-left: 10px;
    box-sizing: border-box;
  }

  .fc-container {
    height: 100%;
  }

  .ant-layout.ant-layout-has-sider {
    height: 100%;
  }

  .center-container {
    height: calc(100% - 8px);
    background-color: #fff;
    padding-bottom: 20px;
    box-sizing: border-box;
    border-left: 8px solid #f0f2f5;
    border-right: 8px solid #f0f2f5;

    .center-header {
      padding: 10px;
    }
  }
</style>
