<template>
  <div class="ah-container">
    <a-layout-header class="btn-bar">
      <slot></slot>
      <a-button v-if="$attrs.uploadJson" type="link" size="small" @click="$emit('uploadJson')">
        <template #icon>
          <div class="btn-icon">
            <SvgIcon name="import" :size="14" />
          </div>
        </template>
        {{ t('导入JSON') }}
      </a-button>
      <a-button v-if="$attrs.generateJson" type="link" size="small" @click="$emit('generateJson')">
        <template #icon>
          <div class="btn-icon">
            <SvgIcon name="printer" :size="14" />
          </div>
        </template>
        {{ t('生成JSON') }}
      </a-button>
      <a-button v-if="$attrs.clearable" type="link" size="small" @click="$emit('clearable')">
        <template #icon>
          <div class="btn-icon">
            <SvgIcon name="clearable" :size="14" />
          </div>
        </template>
        {{ t('清空') }}
      </a-button>
      <a-button v-if="$attrs.preview" type="link" size="small" @click="$emit('preview')">
        <template #icon>
          <div class="btn-icon">
            <SvgIcon name="action" :size="14" />
          </div>
        </template>
        {{ t('预览') }}
      </a-button>

      <a-button v-if="$attrs.generateCode" type="link" size="small" @click="$emit('generateCode')">
        <template #icon>
          <div class="btn-icon">
            <SvgIcon name="generate-code" />
          </div>
        </template>
        {{ t('生成代码') }}
      </a-button>
    </a-layout-header>
  </div>
</template>

<script lang="ts" setup>
  import { SvgIcon } from '/@/components/Icon';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();

  defineEmits(['uploadJson', 'clearable', 'preview', 'generateJson', 'generateCode']);
</script>
<style scoped lang="less">
  .ah-container {
    padding: 0 10px;
    border-bottom: 1px solid #f0f2f5;
  }

  .btn-bar {
    line-height: 46px;
    height: 46px;
    padding: 0 20px;
    background-color: #fff;
    text-align: right;

    .btn-icon {
      display: inline-block;
      border: 1px solid;
      border-radius: 50%;
      width: 25px;
      height: 25px;
    }
  }

  :deep(.ant-btn) > span {
    margin-left: 5px;
  }
  // @import '/@/assets/style/designer/index.css';
</style>
