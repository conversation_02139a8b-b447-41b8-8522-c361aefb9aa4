<template>
  <Draggable
    tag="ul"
    item-key="type"
    ghostClass="ghost"
    :group="{ name: 'people', pull: 'clone', put: false }"
    :sort="false"
    :list="list"
    class="cg-list-container"
    :disabled="isDisabled"
  >
    <template #item="{ element }">
      <li
        v-if="fields.includes(element.type)"
        class="form-edit-widget-label cg-list"
        :class="{ 'no-put': element.type === 'divider' }"
        @click="handleClick(element)"
      >
        <a>
          <SvgIcon :name="element.type ?? element.icon" />
          <span>{{ element.label }}</span>
        </a>
      </li>
    </template>
  </Draggable>
  <div class="clear"></div>
</template>

<script lang="ts" setup>
  import { PropType } from 'vue';
  import Draggable from 'vuedraggable';
  import { SvgIcon } from '/@/components/Icon';

  const props = defineProps({
    title: {
      type: String,
    },
    fields: {
      type: Array as PropType<Array<string>>,
      required: true,
    },
    list: {
      type: Array as PropType<Array<any>>,
      required: true,
    },
    isDisabled: <PERSON><PERSON><PERSON>,
  });
  const emit = defineEmits(['copy']);
  const handleClick = (element: any) => {
    emit('copy', element);
  };
</script>

<style scoped lang="less">
  .left-title {
    height: 14px;
    line-height: 14px;
    padding-left: 10px;
    box-sizing: border-box;
    margin: 15px 0;
    border-left: 6px solid #5e95ff;
    font-weight: bold;
    font-size: 13px;
  }

  .cg-list-container {
    width: 100%;
  }

  .cg-list {
    float: left;
    width: 44%;
    margin-right: 6%;
    margin-bottom: 8px;
    padding: 8px 0;
    text-align: center;
    font-size: 12px;
    border: 1px solid #e4e4e4;
  }

  .cg-list:hover {
    border: 1px dashed #eef4ff;
    background-color: #eef4ff;
    cursor: v-bind("props.isDisabled ? 'not-allowed' : 'pointer'");
  }

  .cg-list:hover a {
    color: #0960bd;
    cursor: v-bind("props.isDisabled ? 'not-allowed' : 'pointer'");
  }

  .cg-list a {
    color: #333;
  }

  .cg-list a svg {
    width: 14px !important;
    height: 14px !important;
    margin-right: 5px;
  }

  .clear {
    clear: both;
  }
  // @import '/@/assets/style/designer/index.css';
</style>
