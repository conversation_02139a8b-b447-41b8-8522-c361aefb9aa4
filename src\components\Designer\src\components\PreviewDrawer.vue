<template>
  <div>
    <BasicDrawer
      v-bind="$attrs"
      size="large"
      @register="registerDrawer"
      @close="handleCloser"
      :title="t('表单预览')"
    >
      <div>
        <SimpleForm ref="formRef" :formProps="state.formProps" :formModel="state.formModel">
        </SimpleForm>
      </div>
    </BasicDrawer>
  </div>
</template>
<script lang="ts" setup>
  import { reactive, ref, inject } from 'vue';
  import SimpleForm from '/@/components/SimpleForm/src/SimpleForm.vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { buildOption } from '/@/utils/helper/designHelper';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const state = reactive({
    formProps: {} as any,
    formModel: {},
  });

  const formRef = ref();

  const isCustomForm = inject<boolean>('isCustomForm', false);

  //使用表单钩子  注册表单  获取到 操作表单的方法

  //使用抽屉内部钩子   获取到 操作抽屉的方法
  const [registerDrawer, { setDrawerProps }] = useDrawerInner(async (option) => {
    //每次进来置空
    setDrawerProps({
      destroyOnClose: true,
      width: option?.config?.formWidth || 800,
      canFullscreen: true,
    });

    setTimeout(() => {
      formRef.value.setProps({
        ...buildOption(option, !isCustomForm),
        showResetButton: true,
        showSubmitButton: true,
      });
      formRef.value.setDefaultValue();
      formRef.value.resetFields();
    }, 50);
  });

  //关闭方法
  const handleCloser = () => {
    formRef.value!.resetFields(); //重置表单
    state.formProps = {};
  };
</script>
