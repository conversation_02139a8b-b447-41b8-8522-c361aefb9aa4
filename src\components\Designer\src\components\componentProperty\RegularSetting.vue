<template>
  <div class="top-box">
    <div class="regular-title">{{ t('正则校验列表') }}</div>
    <a-button type="primary" size="small" @click="addRegular" class="add-btn">
      {{ t('添加正则') }}
    </a-button>
  </div>
  <a-form
    layout="vertical"
    v-for="(item, index) in data.options?.rules"
    :key="index"
    class="reg-item"
    @mouseenter="showDeleteIndex = index"
    @mouseleave="showDeleteIndex = -1"
  >
    <a-form-item :label="t('正则表达式')">
      <a-textarea
        :placeholder="t('请输入正则表达式')"
        v-model:value="item.pattern"
        size="small"
        @blur="checkPattern(item.pattern)"
      />
    </a-form-item>
    <a-form-item :label="t('错误提示')">
      <a-textarea :placeholder="t('请输入错误提示')" v-model:value="item.message" size="small" />
    </a-form-item>
    <div class="delete-btn" v-show="index === showDeleteIndex">
      <SvgIcon name="delete" @click.stop="deleteRegList(index)" class="svg-delete" />
    </div>
  </a-form>
</template>

<script lang="ts" setup>
  import { SvgIcon } from '/@/components/Icon';
  import { message } from 'ant-design-vue';
  import { watch, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    select: { type: Object },
  });
  const emit = defineEmits(['update:select']);
  const data = ref<any>();
  const showDeleteIndex = ref<number>(-1);
  watch(
    () => props.select,
    (val) => {
      data.value = val;
    },
    {
      immediate: true,
      deep: true,
    },
  );
  watch(
    data,
    (val) => {
      emit('update:select', val);
    },
    {
      deep: true,
    },
  );
  const addRegular = () => {
    data.value.options.rules.push({
      pattern: '',
      message: '',
    });
    data.value.options.required = true;
  };
  const deleteRegList = (index) => {
    data.value.options.rules.splice(index, 1);
  };
  const checkPattern = (pattern) => {
    if (!pattern) return;
    const start = pattern.substr(0, 1);
    const end = pattern.substr(-1);
    if (start !== '/' || end !== '/') {
      message.error(t('正则表达式不合法，提醒：是否表达式前后带上了/'));
      return;
    }
  };
</script>

<style lang="less" scoped>
  .top-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 5px 0 10px 10px;

    .regular-title {
      font-size: 14px;
      line-height: 18px;
      padding-left: 6px;
      border-left: 6px solid #5e95ff;
    }
  }

  .reg-item {
    position: relative;
    padding: 5px 0 5px 15px;
    margin-bottom: 18px;

    :deep(.ant-form-item-label) > label {
      font-size: 12px;
    }

    .delete-btn {
      position: absolute;
      display: flex;
      justify-content: center;
      align-items: center;
      top: -3px;
      right: -3px;
      width: 22px;
      height: 22px;
      border: 1px solid #f64c4c;
      color: #f64c4c;
      border-radius: 50%;
      cursor: pointer;

      .svg-delete {
        width: 12px !important;
        height: 12px !important;
      }
    }

    :deep(.ant-form-item) {
      margin-bottom: 10px;
    }

    :deep(.ant-form-item:last-child) {
      margin-bottom: 0;
    }
  }

  // .add-btn {
  //   display: flex;
  //   align-items: center;

  //   /deep/.app-iconify {
  //     display: block !important;
  //   }
  // }
</style>
