<template>
  <a-modal
    :width="1000"
    v-model:visible="visible"
    :title="title"
    :maskClosable="false"
    @ok="handleSubmit"
    @cancel="handleClose"
    :bodyStyle="{ padding: '0 10px 10px' }"
  >
    <div class="list-title">{{ t('字段列表') }}</div>
    <a-table
      :dataSource="dicInfo"
      :columns="dicColumns"
      :pagination="false"
      :scroll="{ y: '400px' }"
    >
      <template #headerCell="{ column }">
        <template v-if="column.key === 'show'">
          {{ t('是否在弹层列表显示') }}
          <a-checkbox v-model:checked="checkedAll" @change="handleChecked" />
        </template>
      </template>
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'component'">
          <a-select
            v-model:value="record.component"
            style="width: 100%"
            :placeholder="t('请选择填充组件')"
            @change="(value) => selectBindField(value, record)"
            allowClear
            :disabled="disabled"
          >
            <a-select-option :value="val.key" v-for="(val, idx) in selectedList" :key="idx">
              {{ val.label }}
            </a-select-option>
          </a-select>
        </template>
        <template v-else-if="column.key === 'tableTitle'">
          <a-input v-model:value="record.tableTitle" :placeholder="t('请填写表头名称')" />
        </template>
        <template v-else-if="column.key === 'show'">
          <a-checkbox v-model:checked="record.show" />
        </template>
        <template v-else-if="column.key === 'width'">
          <a-input
            v-model:value="record.width"
            :disabled="!record.show"
            :placeholder="t('请填写列表宽度')"
          />
        </template>
      </template>
    </a-table>
  </a-modal>
</template>

<script lang="ts" setup>
  import { ref, reactive, watch, onMounted } from 'vue';
  import { ColumnProps } from 'ant-design-vue/lib/table/Column';
  import { message } from 'ant-design-vue';
  import { cloneDeep } from 'lodash-es';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    dicAssoDia: { type: Boolean },
    dicOptions: { type: Object as PropType<any> },
    selectedList: { type: Array as any },
    type: { type: String },
    disabled: { type: Boolean, default: () => false },
  });
  const emit = defineEmits(['update:dicAssoDia', 'update:dicOptions']);
  const dicInfo = ref<any[]>([]);
  const title = ref<string>('');
  const checkedAll = ref<boolean>(false);
  let dicColumns;
  const visible = ref<boolean>(props.dicAssoDia);

  watch(
    () => props.dicOptions,
    (val) => {
      if (val && val.length) {
        dicInfo.value = cloneDeep(val);
      }
    },
    {
      immediate: true,
      deep: true,
    },
  );

  watch(
    () => dicInfo.value,
    (val) => {
      checkedAll.value = val.every((item: any) => {
        return item.show;
      });
    },
    {
      immediate: true,
      deep: true,
    },
  );

  onMounted(() => {
    if (!dicInfo.value.length) {
      switch (props.type) {
        case 'button':
        case 'associate-popup':
          dicInfo.value = [
            {
              dataIndex: 'name',
              title: 'name',
              name: 'name',
              bindField: '',
              tableTitle: '',
              show: true,
              width: 150,
            },
            {
              dataIndex: 'value',
              title: 'value',
              name: 'value',
              bindField: '',
              tableTitle: '',
              show: true,
              width: 150,
            },
          ];
          break;
        case 'multiple-popup':
          dicInfo.value = [
            {
              dataIndex: 'name',
              title: 'name',
              name: 'name',
              tableTitle: '',
              show: true,
              width: 150,
            },
            {
              dataIndex: 'value',
              title: 'value',
              name: 'value',
              tableTitle: '',
              show: true,
              width: 150,
            },
          ];
          break;
        case 'associate-select':
          dicInfo.value = [
            {
              dataIndex: 'name',
              title: 'name',
              name: 'name',
              bindField: '',
            },
            {
              dataIndex: 'value',
              title: 'value',
              name: 'value',
              bindField: '',
            },
          ];
          break;
        case 'preload-title':
          dicInfo.value = [
            {
              dataIndex: 'name',
              title: 'name',
              name: 'name',
              tableTitle: 'name',
            },
            {
              dataIndex: 'value',
              title: 'value',
              name: 'value',
              tableTitle: 'value',
            },
          ];
          break;
      }
    }
  });
  const multiplePopupColumns = reactive<ColumnProps[]>([
    {
      title: '#',
      align: 'center',
      customRender: ({ index }) => `${index + 1}`, // 显示每一行的序号
      width: 80,
    },
    {
      title: t('字段名'),
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: t('表头名称'),
      dataIndex: 'tableTitle',
      key: 'tableTitle',
      align: 'center',
      width: 200,
    },
    {
      title: t('显示'),
      dataIndex: 'show',
      key: 'show',
      align: 'center',
      width: 200,
    },
    {
      title: t('列表宽度'),
      dataIndex: 'width',
      key: 'width',
      align: 'center',
      width: 150,
    },
  ]);

  const associatePopupColumns = reactive<ColumnProps[]>([
    {
      title: '#',
      align: 'center',
      customRender: ({ index }) => `${index + 1}`, // 显示每一行的序号
      width: 80,
    },
    {
      title: t('字段名'),
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: t('填充组件'),
      dataIndex: 'component',
      key: 'component',
      align: 'center',
      width: 250,
    },
    {
      title: t('表头名称'),
      dataIndex: 'tableTitle',
      key: 'tableTitle',
      align: 'center',
      width: 200,
    },
    {
      title: t('是否在弹层列表显示'),
      dataIndex: 'show',
      key: 'show',
      align: 'center',
      width: 200,
    },
    {
      title: t('列表宽度'),
      dataIndex: 'width',
      key: 'width',
      align: 'center',
      width: 150,
    },
  ]);

  const associateSelectColumns = reactive<ColumnProps[]>([
    {
      title: '#',
      align: 'center',
      customRender: ({ index }) => `${index + 1}`, // 显示每一行的序号
      width: 80,
    },
    {
      title: t('字段名'),
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: t('填充组件'),
      dataIndex: 'component',
      key: 'component',
      align: 'center',
    },
    {
      title: t('填充组件绑定字段'),
      dataIndex: 'bindField',
      key: 'bindField',
      align: 'center',
    },
  ]);

  const preloadColumns = reactive<ColumnProps[]>([
    {
      title: '#',
      align: 'center',
      customRender: ({ index }) => `${index + 1}`, // 显示每一行的序号
      width: 80,
    },
    {
      title: t('字段名'),
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: t('表头名称'),
      dataIndex: 'tableTitle',
      key: 'tableTitle',
      align: 'center',
    },
  ]);
  watch(
    () => props.type,
    (val) => {
      console.log('props.type', val);
      switch (val) {
        case 'button':
        case 'associate-popup':
          dicColumns = associatePopupColumns;
          title.value = t('联想数据配置-数据字典');
          break;
        case 'multiple-popup':
          dicColumns = multiplePopupColumns;
          title.value = t('多选数据配置-数据字典');
          break;
        case 'associate-select':
          dicColumns = associateSelectColumns;
          title.value = t('联想数据配置-数据字典');
          break;
        case 'preload-title':
          dicColumns = preloadColumns;
          title.value = t('表头配置-数据字典');
          break;
      }
    },
    { immediate: true },
  );

  const selectBindField = (value, record) => {
    if (!value || !isNaN(value)) {
      message.error(t('请先选择该组件的绑定表及绑定字段'));
      record.bindField = null;
      record.bindTable = undefined;
    } else {
      let obj = props.selectedList.find((o) => o.key == value);
      if (obj) {
        record.bindField = obj.bindField;
        if (obj.isSingleFormChild || obj.isSubFormChild) {
          record.bindTable = obj.bindTable;
        } else {
          record.bindTable = undefined;
        }
      }
    }
  };

  const handleChecked = (e: any) => {
    dicInfo.value.map((item: any) => (item.show = e.target.checked));
  };

  const handleClose = () => {
    emit('update:dicAssoDia', false);
  };

  const handleSubmit = () => {
    const bindFieldArr = [] as any;
    dicInfo.value?.map((item: any) => {
      if (item.bindField) {
        bindFieldArr.push(item.bindField);
      }
    });
    if (new Set(bindFieldArr).size !== bindFieldArr.length) {
      message.error(t('组件存在重复绑定,请更改'));
      return;
    }
    emit('update:dicAssoDia', false);
    emit('update:dicOptions', dicInfo.value);
  };
</script>

<style lang="less" scoped>
  .list-title {
    margin: 10px 0 5px;
    font-size: 14px;
    line-height: 16px;
    padding-left: 6px;
    border-left: 6px solid #5e95ff;
  }
</style>
