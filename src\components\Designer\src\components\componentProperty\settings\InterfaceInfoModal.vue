<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="t('查看接口信息')" @ok="closeModal">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="1" :tab="t('接口信息')">
        <CodeEditor :value="script" language="json" readonly />
      </a-tab-pane>
      <a-tab-pane key="2" :tab="t('接口出参示例')">
        <CodeEditor :value="infoExample" language="json" readonly />
      </a-tab-pane>
    </a-tabs>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { CodeEditor } from '/@/components/CodeEditor';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const activeKey = ref<string>('1');
  const script = ref<string>('');
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    script.value = data.script;
    setModalProps({
      confirmLoading: false,
      draggable: false,
      showOkBtn: false,
      showCancelBtn: false,
      destroyOnClose: true,
      width: 800,
    });
  });

  const infoExample = JSON.stringify({
    code: 0,
    msg: t('提示信息'),
    data: [
      {
        label: t('选项一'),
        value: 1,
      },
      {
        label: t('选项二'),
        value: 1,
      },
      {
        label: t('选项三'),
        value: 1,
      },
    ],
  });
</script>
