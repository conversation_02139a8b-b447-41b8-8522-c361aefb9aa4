<template>
  <div class="static-box">
    <div class="static-top">
      <div class="static-title">{{ t('静态数据列表') }}</div>
      <div>
        <a-button
          :disabled="disabled"
          type="primary"
          size="small"
          @click="handleReset"
          class="mr-0.5"
        >
          取消选中
        </a-button>
        <a-button
          :disabled="disabled"
          type="primary"
          size="small"
          @click="emits('handleInsertOption')"
        >
          {{ t('添加数据') }}
        </a-button>
      </div>
    </div>
    <a-table
      :dataSource="options?.staticOptions"
      :row-selection="{
        type: type,
        selectedRowKeys: selectedRowKey,
        onChange: onChange,
      }"
      :pagination="false"
      :columns="columns"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'label'">
          <a-input
            v-model:value="record[column.key]"
            :disabled="disabled"
            :placeholder="t('请输入')"
          />
        </template>
        <template v-if="column.key === 'value'">
          <a-input
            v-model:value="record[column.key]"
            :disabled="disabled"
            :placeholder="t('请输入')"
            @change="(val) => changeValue(val, record.key)"
          />
        </template>
        <template v-if="column.key === 'action'">
          <DeleteTwoTone two-tone-color="#ff8080" @click="emits('handleOptionsRemove', index)" />
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
  import { ref, reactive, watch, onMounted } from 'vue';
  import { DeleteTwoTone } from '@ant-design/icons-vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const props = defineProps({
    options: Object,
    type: String,
    disabled: { type: Boolean, default: () => false },
  });
  const emits = defineEmits(['handleOptionsRemove', 'handleInsertOption', 'handleDefaultSelect']);
  const selectedRowKey = ref<string[] | number[]>([1]);
  const selectedRow = ref<any[]>([]);

  const columns = reactive([
    {
      title: t('显示值'),
      dataIndex: 'label',
      key: 'label',
      align: 'center',
    },
    {
      title: t('保存值'),
      dataIndex: 'value',
      key: 'value',
      align: 'center',
    },
    {
      title: t('操作'),
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      width: 50,
    },
  ]);

  watch(
    () => props.options,
    (val) => {
      if (props.type === 'radio') {
        //单选组件、下拉选择
        if (!!val?.defaultSelect) {
          val.staticOptions.map((item: any, index: number) => {
            if (item.value === val?.defaultSelect) {
              selectedRowKey.value = [index + 1];
            }
          });
        }
      } else {
        //多选组件
        if (val?.defaultSelect) {
          selectedRowKey.value = [];
          val.staticOptions.map((item: any, index: number) => {
            val?.defaultSelect.split(',').map((select: [string, number]) => {
              if (item.value === select) {
                (selectedRowKey.value as number[]).push(index + 1);
              }
            });
          });
        } else {
          selectedRowKey.value = [];
        }
      }
    },
    { deep: true, immediate: true },
  );

  onMounted(() => {
    selectedRowKey.value = [props.options?.staticOptions[0].key];
    selectedRow.value = [props.options?.staticOptions[0]];
    emits('handleDefaultSelect', props.options?.staticOptions[0].value);
  });

  watch(
    () => selectedRow.value,
    (val) => {
      const selected = val?.map((x) => x.value).join(',');
      emits('handleDefaultSelect', selected);
    },
    { deep: true },
  );

  const onChange = (selectedRowKeys: string[] | number[], selectedRows: any[]) => {
    if (props.disabled) return;
    selectedRowKey.value = selectedRowKeys;
    selectedRow.value = selectedRows;
    let selectedValue;
    if (props.type === 'radio') {
      selectedValue = selectedRows[0].value;
    } else {
      selectedValue = selectedRows.map((item) => item.value).join(',');
    }
    emits('handleDefaultSelect', selectedValue);
  };

  const handleReset = () => {
    selectedRowKey.value = [];
    selectedRow.value = [];
    emits('handleDefaultSelect', '');
  };

  const changeValue = (e, key) => {
    selectedRow.value.map((row) => {
      if (row.key === key) {
        row.value = e.target.value;
      }
    });
  };
</script>

<style lang="less" scoped>
  .static-box {
    padding: 5px 0 10px 20px;

    :deep(.ant-table) {
      font-size: 14px;
    }

    :deep(.ant-table-tbody tr td) {
      padding: 8px;
    }

    :deep(.ant-table .ant-table-thead tr th) {
      padding: 8px;
    }

    .static-top {
      display: flex;
      justify-content: space-between;
      font-size: 14px;
      margin-top: 5px;

      .static-title {
        padding-left: 6px;
        border-left: 6px solid #5e95ff;
      }
    }
  }
</style>
