<template>
  <div class="fc-style">
    <a-layout class="center-container">
      <a-layout-content :class="!json ? 'widget-empty' : 'widget-full'">
        <grid-layout :layout="json" :row-height="64" :is-draggable="false" :is-resizable="false">
          <grid-item
            v-for="item in json"
            :x="item.x"
            :y="item.y"
            :w="item.w"
            :h="item.h"
            :i="item.i"
            :key="item.i"
          >
            <DefaultItem
              v-if="
                desktop.basicComponents.map((x) => x.type).includes(item.type) ||
                desktop.chartComponents.map((x) => x.type).includes(item.type) ||
                desktop.systemComponents.map((x) => x.type).includes(item.type)
              "
              :data="item.props"
              :name="item.type"
            />
            <CustomItem v-else :data="item.props" :name="item.type" />
          </grid-item>
        </grid-layout>
      </a-layout-content>
    </a-layout>
  </div>
</template>

<script lang="ts">
  import { defineComponent, reactive, PropType, toRefs } from 'vue';

  import * as desktop from '../types';
  import DefaultItem from './base/DefaultItem.vue';
  import CustomItem from './base/CustomItem.vue';

  export default defineComponent({
    name: 'DesktopPerview',
    components: {
      DefaultItem,
      CustomItem,
    },
    props: {
      json: {
        type: Array as PropType<Array<any>>,
        default: () => [],
      },
    },
    setup() {
      const state = reactive({
        desktop,
      });

      return {
        ...toRefs(state),
      };
    },
  });
</script>
<style scoped lang="less">
  @import '/@/assets/style/designer/index.css';
</style>
