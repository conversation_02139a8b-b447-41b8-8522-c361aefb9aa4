<template>
  <component :is="componentName" v-model:data="config" />
</template>
<script lang="ts" setup>
  import { computed, ref, watch } from 'vue';

  const props = defineProps({
    select: Object,
  });

  const emit = defineEmits(['update:data']);

  const config = ref(props.select?.props);

  watch(props, (val) => {
    config.value = val.select?.props;
  });

  watch(config, (val) => {
    emit('update:data', val);
  });

  const componentName = computed(() => {
    return props.select?.type?.toLowerCase() + '-config';
  });
</script>
