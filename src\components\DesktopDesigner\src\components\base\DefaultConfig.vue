<template>
  <DashBoard v-if="componentName === 'dashboard'" v-model:data="config" />

  <InfoList v-if="componentName === 'infolist'" v-model:data="config" />
</template>
<script lang="ts" setup>
  import { computed, watch, ref } from 'vue';
  import DashBoard from '../dashboard/config.vue';
  import InfoList from '../infolist/config.vue';

  const emit = defineEmits(['update:data']);

  const props = defineProps({
    select: Object,
  });

  const config = ref(props.select?.props);

  const componentName = computed(() => {
    return props.select?.type?.toLowerCase();
  });

  watch(props, (val) => {
    config.value = val.select?.props;
  });

  watch(config, (val) => {
    emit('update:data', val);
  });
</script>
