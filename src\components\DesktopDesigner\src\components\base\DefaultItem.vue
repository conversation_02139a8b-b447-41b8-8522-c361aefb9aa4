<template>
  <DashBoard v-if="componentName === 'dashboard'" :data="data" />
  <InfoList v-if="componentName === 'infolist'" :data="data" />
</template>
<script lang="ts" setup>
  import { computed } from 'vue';
  import DashBoard from '../dashboard/view.vue';
  import InfoList from '../infolist/view.vue';

  const props = defineProps({
    name: String,
    data: Object,
  });

  const componentName = computed(() => {
    return props.name?.toLowerCase();
  });
</script>
