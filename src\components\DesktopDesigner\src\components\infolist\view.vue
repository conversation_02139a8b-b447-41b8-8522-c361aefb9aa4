<template>
  <div ref="chartRef" :style="{ width: '100%', height: '100%' }"></div>
</template>
<script lang="ts" setup>
  import * as echarts from 'echarts';
  import { ref, unref, onMounted } from 'vue';

  const chartRef = ref<HTMLDivElement>();

  onMounted(() => {
    // 基于准备好的dom，初始化echarts实例
    var myChart = echarts.init(unref(chartRef) as HTMLDivElement);
    // 绘制图表
    myChart.setOption({
      title: {
        text: 'ECharts 入门示例',
      },
      tooltip: {},
      xAxis: {
        data: ['衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', '袜子'],
      },
      yAxis: {},
      series: [
        {
          name: '销量',
          type: 'bar',
          data: [5, 20, 36, 10, 10, 20],
        },
      ],
    });
  });

  defineProps({
    data: Object,
  });
</script>
<style></style>
