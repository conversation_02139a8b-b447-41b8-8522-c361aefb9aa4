<template>
  <div>
    <TreeSelect
      v-model:value="value"
      :disabled="disabled"
      show-search
      :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
      placeholder="请选择数据选项"
      tree-default-expand-all
      :tree-data="data"
      :fieldNames="fieldNames"
      style="width: 100%"
    />
    <!-- <a-select
      v-model:value="value"
      show-search
      :disabled="disabled"
      :placeholder="t('请选择数据选项')"
      :mode="mode"
      :options="data"
      :filter-option="false"
      :fieldNames="fieldNames"
      style="width: 100%"
      @search="handleSearch"
    /> -->
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, ref, unref, watch } from 'vue';
  import { getDicTreeList } from '/@/api/system/dic';
  // import { useI18n } from '/@/hooks/web/useI18n';
  import { cloneDeep } from 'lodash-es';
  import { TreeSelect } from 'ant-design-vue';
  // const { t } = useI18n();
  const emit = defineEmits(['update:value', 'change']);

  const props = defineProps({
    value: String,
    disabled: { type: Boolean, default: () => false },
    mode: String,
  });

  const value = ref(props.value);

  const data = ref<Recordable[]>([]);
  const options = ref<Recordable[]>([]);
  const fieldNames = ref({
    label: 'name',
    value: 'id',
  });

  onMounted(() => {
    fetch();
  });

  watch(
    () => value,
    () => {
      emit('update:value', unref(value)?.toString());
      emit('change', unref(value)?.toString());
    },
    { deep: true },
  );

  async function fetch() {
    data.value = await getDicTreeList();

    data.value.map((item) => {
      item.disabled = true;
      item.value = item.id; //避免数字字典项没有value导致treeselect组件报错
    });
    options.value = cloneDeep(data.value);
    value.value = props.value;
  }

  // function handleSearch(val) {
  //   data.value = options.value.filter((item) => {
  //     return item.name.includes(val);
  //   });
  // }
</script>
