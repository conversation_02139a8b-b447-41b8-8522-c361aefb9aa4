import { defineAsyncComponent, type Component } from 'vue';
import type { ComponentType } from './types/index';

/**
 * Component list, register here to setting it in the form
 */
import {
  Input,
  InputNumber,
  Select,
  Radio,
  Checkbox,
  DatePicker,
  TreeSelect,
  Rate,
  Divider,
} from 'ant-design-vue';
const DatasourceSelect = defineAsyncComponent({
  loader: () => import('/@/components/DataSourceSelect/src/DatasourceSelect.vue'),
});

const FormView = defineAsyncComponent({
  loader: () => import('./components/FormView.vue'),
});

import ApiRadioGroup from './components/ApiRadioGroup.vue';
import ApiCheckboxGroup from './components/ApiCheckboxGroup.vue';
import RadioButtonGroup from './components/RadioButtonGroup.vue';
import ApiSelect from './components/ApiSelect.vue';
import ApiTree from './components/ApiTree.vue';
import ApiTreeSelect from './components/ApiTreeSelect.vue';
import ApiCascader from './components/ApiCascader.vue';
import SelectDepartment from './components/SelectDepartment.vue';
import SelectUser from './components/SelectUser.vue';
import CommonInfo from './components/CommonInfo.vue';
import SelectArea from './components/SelectArea.vue';
import AutoCodeRule from './components/AutoCodeRule.vue';
import MoneyChineseInput from './components/MoneyChineseInput.vue';
import Image from './components/Image.vue';
import Upload from './components/Upload.vue';
import Upload2 from './components/Upload2.vue';
import SelectMap from './components/SelectMap.vue';
import XjrQrcode from './components/QrCode.vue';
import AutoComplete from './components/ApiComplete.vue';
import XjrIframe from './components/XjrIframe.vue';
import BarCode from './components/BarCode.vue';
import Signature from './components/Signature.vue';
import NetworkSelect from '/@/components/NetworkSelect/src/NetworkSelect.vue';

import File from './components/File.vue';

import SelectForm from './components/SelectForm.vue';
import SelectDesignList from './components/SelectDesignList.vue';

import { XjrInput } from '/@/components/Input';
import { XjrInputPassword } from '/@/components/InputPassword';
import { XjrLabelComponent } from '/@/components/LabelComponent';
import { XjrSwitch } from '/@/components/Switch';
import { XjrSelect } from '/@/components/Select';
import { ColorPicker } from '/@/components/ColorPicker';
import { StrengthMeter } from '/@/components/StrengthMeter';
import { IconPicker } from '/@/components/Icon';
import { CountdownInput } from '/@/components/CountDown';
import { ChildTable } from '/@/components/ChildTable';
import { Text } from '/@/components/Text';
import { DicSelect } from '/@/components/DicSelect';
import { DbSelect } from '/@/components/DbSelect';
import { RichTextEditor } from '/@/components/RichTextEditor';

import { DicItemSelect } from '/@/components/DicItemSelect';
import { MenuSelect } from '/@/components/MenuSelect';
import { MultiplePopup } from '/@/components/MultiplePopup';
import { AssociateSelect } from '/@/components/AssociateSelect';
import { Computation } from '/@/components/Computation';
import { Button } from '/@/components/ButtonForm';
import { Title } from '/@/components/Title';
import { Opinion } from '/@/components/Opinion';
import { TimePicker } from '/@/components/TimePicker';
import { TimeRangePicker } from '/@/components/TimeRangePicker';
import { RangePicker } from '/@/components/RangePicker';
import { XjrDatePicker } from '/@/components/DatePicker';
import { Slider } from '/@/components/Slider';
import { CodeTextArea } from '/@/components/Input';
import { OneForOne } from '/@/components/OneForOne';
import SubForm from './components/SubForm.vue';
import SunForm from './components/SunForm.vue';

import { TreeComponent, TreeSelectComponent } from '/@/components/TreeStructure';
const componentMap = new Map<ComponentType, Component>();

componentMap.set('Input', XjrInput);
componentMap.set('InputGroup', Input.Group);
componentMap.set('InputPassword', XjrInputPassword);
componentMap.set('LabelComponent', XjrLabelComponent);
componentMap.set('InputSearch', Input.Search);
componentMap.set('InputTextArea', Input.TextArea);
componentMap.set('InputNumber', InputNumber);
// componentMap.set('AutoComplete', AutoComplete);
componentMap.set('AutoCodeRule', AutoCodeRule);
componentMap.set('MoneyChineseInput', MoneyChineseInput);
componentMap.set('DatasourceSelect', DatasourceSelect);
componentMap.set('Select', Select);
componentMap.set('XjrSelect', XjrSelect);
componentMap.set('ApiSelect', ApiSelect);
componentMap.set('ApiTree', ApiTree);
componentMap.set('TreeSelect', TreeSelect);
componentMap.set('ApiTreeSelect', ApiTreeSelect);
componentMap.set('ApiRadioGroup', ApiRadioGroup);
componentMap.set('ApiCheckboxGroup', ApiCheckboxGroup);
componentMap.set('Switch', XjrSwitch);
componentMap.set('RadioButtonGroup', RadioButtonGroup);
componentMap.set('RadioGroup', Radio.Group);
componentMap.set('Checkbox', Checkbox);
componentMap.set('CheckboxGroup', Checkbox.Group);
componentMap.set('ApiCascader', ApiCascader);
componentMap.set('Slider', Slider);
componentMap.set('Rate', Rate);
componentMap.set('Dept', SelectDepartment);
componentMap.set('User', SelectUser);
componentMap.set('Info', CommonInfo);
componentMap.set('Area', SelectArea);
componentMap.set('SubForm', SubForm);
componentMap.set('SunForm', SunForm);
componentMap.set('Button', Button);
componentMap.set('SelectMap', SelectMap);
componentMap.set('XjrQrcode', XjrQrcode);
componentMap.set('TreeComponent', TreeComponent); //树结构 树形组件
componentMap.set('TreeSelectComponent', TreeSelectComponent); //树形选择组件
componentMap.set('FormView', FormView);
componentMap.set('XjrIframe', XjrIframe);
componentMap.set('BarCode', BarCode);

componentMap.set('SelectForm', SelectForm);
componentMap.set('SelectDesignList', SelectDesignList);

componentMap.set('DatePicker', XjrDatePicker);
componentMap.set('MonthPicker', DatePicker.MonthPicker);
componentMap.set('RangePicker', RangePicker);
componentMap.set('WeekPicker', DatePicker.WeekPicker);
componentMap.set('TimePicker', TimePicker);
componentMap.set('TimeRangePicker', TimeRangePicker);
componentMap.set('StrengthMeter', StrengthMeter);
componentMap.set('IconPicker', IconPicker);
componentMap.set('InputCountDown', CountdownInput);
componentMap.set('ChildTable', ChildTable);
componentMap.set('Upload', Upload);
componentMap.set('Upload2', Upload2);
componentMap.set('Divider', Divider);
componentMap.set('Text', Text);
componentMap.set('RichTextEditor', RichTextEditor);
componentMap.set('MultiplePopup', MultiplePopup);
componentMap.set('AssociateSelect', AssociateSelect);
componentMap.set('Computation', Computation);
componentMap.set('ColorPicker', ColorPicker);
componentMap.set('Title', Title);
componentMap.set('Opinion', Opinion);
componentMap.set('Image', Image);
componentMap.set('File', File);

componentMap.set('DicSelect', DicSelect);
componentMap.set('DbSelect', DbSelect);
componentMap.set('DicItemSelect', DicItemSelect);
componentMap.set('MenuSelect', MenuSelect);
componentMap.set('CodeTextArea', CodeTextArea);
componentMap.set('OneForOne', OneForOne);
componentMap.set('AutoComplete', AutoComplete);
componentMap.set('Signature', Signature);
componentMap.set('NetworkSelect', NetworkSelect);

export function add(compName: ComponentType, component: Component) {
  componentMap.set(compName, component);
}

export function del(compName: ComponentType) {
  componentMap.delete(compName);
}

export { componentMap };
