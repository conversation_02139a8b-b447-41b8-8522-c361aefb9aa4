<template>
  <div v-if="fileList && fileList.length" class="file-box">
    <div v-for="item in fileList" :key="item.id" class="flex justify-between mb-1.5">
      <div class="flex">
        <img :src="getImg(item.fileSuffiex)" class="w-5 mr-1.5" />
        {{ item.fileName + item.fileType }}
      </div>
      <div>
        <Icon
          icon="ant-design:eye-outlined"
          @click="handlePreview(item)"
          class="mr-2.5 cursor-pointer"
        />
        <Icon
          icon="ant-design:download-outlined"
          @click="handleDownload(item)"
          class="mr-2.5 cursor-pointer"
        />
        <Icon
          v-if="isShowDel"
          icon="ant-design:delete-outlined"
          @click="handleDelete(item)"
          class="cursor-pointer"
        />
      </div>
    </div>
  </div>
  <img :src="filePng" style="width: 100px" v-else />
  <PreviewModal @register="registerPreviewModal" />
</template>
<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { getFileList, deleteSingleFile } from '/@/api/system/file';
  import { FilePageListModel } from '/@/api/system/file/model';
  import PreviewModal from '/@/views/dataconfig/fileManagement/components/PreviewModal.vue';
  import { downloadByUrl } from '/@/utils/file/download';
  import { useModal } from '/@/components/Modal';
  import { Icon } from '/@/components/Icon';
  import filePng from '/@/assets/file/file.png';
  import pdfPng from '/@/assets/file/pdf.png';
  import wordPng from '/@/assets/file/word.png';
  import txtPng from '/@/assets/file/txt.png';
  import imgPng from '/@/assets/file/img.png';
  import excelPng from '/@/assets/file/excel.png';
  import defaultFile from '/@/assets/file/defaultFile.png';

  const props = defineProps({
    defaultValue: String,
    disabled: Boolean,
    isShowDel: {
      type: Boolean,
      default: false,
    },
  });

  const emit = defineEmits(['update:defaultValue']);

  const fileList = ref<FilePageListModel[]>([]);

  watch(
    () => props.defaultValue,
    async (val) => {
      if (val) {
        let result = await getFileList({ folderId: props.defaultValue });
        if (result.length) {
          fileList.value = result;
        }
      }
      if (!val) {
        fileList.value = [];
      }
    },
    {
      immediate: true,
    },
  );
  const [registerPreviewModal, { openModal: openPreviewModal }] = useModal();

  const handlePreview = async (record) => {
    openPreviewModal(true, {
      fileUrl: record.fileUrl,
      fileName: record.fileName,
    });
  };

  const getImg = (type) => {
    switch (type) {
      case 'pdf':
        return pdfPng;
      case 'doc':
      case 'docx':
        return wordPng;
      case 'txt':
        return txtPng;
      case 'png':
      case 'jpg':
      case 'jpeg':
        return imgPng;
      case 'xlsx':
      case 'xls':
        return excelPng;
      default:
        return defaultFile;
    }
  };

  const handleDownload = async (record) => {
    downloadByUrl({ url: record.fileUrl, fileName: record.fileName });
  };

  const handleDelete = async (record) => {
    const newFolderId = await deleteSingleFile(record.id);
    emit('update:defaultValue', newFolderId);
  };
</script>
<style lang="less" scoped>
  .file-box {
    width: 400px;
    height: 140px;
    overflow: auto;
  }
</style>
