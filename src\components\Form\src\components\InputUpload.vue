<template>
  <div>
    <a-upload
      class="InputUpload w-full"
      accept="image/png, image/jpeg, image/jpg"
      :beforeUpload="beforeUpload"
      listType="text"
      :show-upload-list="false"
      @remove="handleRemove"
      @download="handleDownload"
      @preview="handlePreview"
      @click="handleClick"
    >
      <a-input-group compact class="!flex items-center">
        <a-input :placeholder="placeholder" :value="name">
          <template #suffix>
            <Icon icon="fa-solid:upload" color="#ccc" />
            <CloseCircleFilled
              @click.stop="handleClear"
              style="position: absolute; right: 40px; color: #ccc; cursor: pointer; z-index: 9999"
            />
          </template>
        </a-input>
        <a-popover placement="topRight">
          <template #content>
            <a-image
              v-if="tipType == 'img'"
              :width="280"
              :src="tips"
              fallback="data:image/png;base64,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"
            />
          </template>
          <QuestionCircleFilled
            v-if="showTip"
            style="width: 50px; font-size: 20px; color: #ccc; border: 0"
          />
        </a-popover>
      </a-input-group>
    </a-upload>
    <a-modal
      :bodyStyle="bodyStyle"
      :width="800"
      :visible="previewVisible"
      :title="previewTitle"
      :footer="null"
      @cancel="handleCancel"
    >
      <iframe :src="previewFile" class="iframe-box"></iframe>;
    </a-modal>
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch } from 'vue';
  import { Upload } from 'ant-design-vue';
  import { QuestionCircleFilled, CloseCircleFilled } from '@ant-design/icons-vue';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { deleteSingleFile, getFileList } from '/@/api/system/file';
  import { downloadByUrl } from '/@/utils/file/download';
  import { uploadMultiApi } from '/@/api/sys/upload';
  import { Icon } from '/@/components/Icon';
  import { Base64 } from 'js-base64';
  import { getAppEnvConfig } from '/@/utils/env';

  const props = defineProps({
    value: String,
    showTip: { type: Boolean, default: true },
    placeholder: String,
    tipType: String,
    tips: String,
    maxSize: Number,
  });

  const fileList = ref<any[]>([]);
  const list = ref<any[]>([]);
  const { notification } = useMessage();
  const folderId = ref<string>('');
  const deleteFlag = ref(false);
  const emit = defineEmits(['update:value', 'change', 'click']);
  const loading = ref(false);
  const name = ref();
  const previewVisible = ref(false);
  const previewFile = ref('');
  const previewTitle = ref('');

  watch(
    () => props.value,
    async (val) => {
      if (val) {
        fileList.value = await getFileList({ folderId: props.value });
        if (fileList.value.length) {
          fileList.value.forEach((x) => {
            x.name = x.fileName;
            x.url = x.fileUrl;
            x.thumbUrl = x.thUrl;
            x.status = 'done'; //没有则不会展示下载按钮
          });
          folderId.value = fileList.value[0].folderId;
          name.value = fileList.value[0].name + fileList.value[0].fileType;
        }
      }
      if (!val) {
        fileList.value = [];
        name.value = '';
      }
    },
    {
      immediate: true,
    },
  );
  watch(
    () => list.value,
    async (val) => {
      if (deleteFlag.value) return;
      if (val.length) {
        let arr: any[] = val.filter((o) => {
          return !o.status;
        });
        if (arr.length <= 0) return;
        try {
          let res = await uploadMultiApi(
            {
              name: 'file',
              file: arr,
            },
            folderId.value,
          );
          folderId.value = res[0].folderId;
          fileList.value.forEach((x) => {
            x.status = 'done'; //没有则不会展示下载按钮
            x.url = x.fileUrl;
            x.thumbUrl = x.thUrl;
          });
          name.value = res[0].fileName + res[0].fileType;
          emit('update:value', folderId.value);
          emit('change');
          loading.value = false;
        } catch (error) {
          console.error(error);
          loading.value = false;
        }
      }
    },
  );

  const bodyStyle = { height: '500px' };

  const beforeUpload = (file) => {
    if (props.maxSize && file.size / 1024 / 1024 > props.maxSize) {
      notification.error({
        message: 'Tip',
        description: `文件大小不能超过${props.maxSize}MB！`,
      });
      return false || Upload.LIST_IGNORE;
    }

    list.value = [...list.value, file];
    deleteFlag.value = false;
    loading.value = true;
    return Upload.LIST_IGNORE;
  };
  function handleClick() {
    list.value = [];
  }
  const handleRemove = async (info) => {
    const id = info.response ? info.response.data.id : info.id;

    const newFolderId = await deleteSingleFile(id);

    folderId.value = newFolderId;

    deleteFlag.value = true;

    const index = fileList.value.findIndex((x) => x.id === id);
    fileList.value.splice(index, 1);

    fileList.value.forEach((x) => {
      x.folderId = newFolderId;
    });

    emit('update:value', folderId.value);
    emit('change');
    notification.success({
      message: 'Tip',
      description: '删除成功！',
    });
  };

  const handleDownload = (info) => {
    const url = info.response ? info.response.data.fileUrl : info.fileUrl;
    const fileName = info.response ? info.response.data.fileName : info.fileName;

    downloadByUrl({ url, fileName });
  };

  const handlePreview = async (file) => {
    const fileUrl = file.response?.data?.fileUrl || file.fileUrl;
    previewFile.value =
      getAppEnvConfig().VITE_GLOB_UPLOAD_PREVIEW +
      encodeURIComponent(
        Base64.encode(
          fileUrl.includes('http://') || fileUrl.includes('https://')
            ? fileUrl
            : getAppEnvConfig().VITE_GLOB_API_URL + fileUrl,
        ),
      );

    previewVisible.value = true;
    previewTitle.value = file.name || file.fileName;
  };

  const handleCancel = () => {
    previewVisible.value = false;
    previewTitle.value = '';
  };

  function handleClear() {
    name.value = '';
    folderId.value = '';
    emit('update:value', folderId.value);
    emit('change');
  }
</script>
<style lang="less" scoped>
  .InputUpload {
    :deep(.ant-upload) {
      width: 100%;
    }

    :deep(.anticon-plus) {
      font-size: 20px;
      color: #999;
    }

    .dragger-tip {
      background: rgb(0 0 0 / 60%);
      color: #fff;
      height: 100%;
      border-radius: 2px;
      display: none;
      padding: 8px 5px;
      width: 100%;
    }

    .dragger-text {
      padding: 16px 0;
    }

    &:hover .dragger-tip {
      display: block;
    }

    &:hover .dragger-text {
      display: none;
    }
  }

  :deep(.ant-upload) {
    padding: 0 !important;
  }

  .iframe-box {
    width: 100%;
    height: 100%;
  }
</style>
