<script lang="tsx">
  import { PropType, ref } from 'vue';
  import { defineComponent } from 'vue';
  import type { FormSchema } from '../types/form';
  import { componentMap } from '../componentMap';

  const MyFormItem = defineComponent({
    name: 'SearchFormItem',
    inheritAttrs: false,
    props: {
      schema: {
        type: Object as PropType<FormSchema>,
        default: () => ({}),
        /*
        {
        component: "Input"
        field: "duo_xing_wen_ben4404"
        label: "多行文本",
        componentProps:{}
        }
        */
      },
      value: [Array, Object, String, Number],
    },
    emits: ['change'],
    setup(props, { emit }) {
      const myFormModalValue = ref(props.value);
      function emitChange() {
        emit('change', myFormModalValue.value);
      }

      function renderComponent() {
        const { component, componentProps } = props.schema;
        emitChange();
        const Comp = componentMap.get(component) as ReturnType<typeof defineComponent>;

        const propsData = {
          allowClear: true,
          class: component == 'Input' ? 'ant-input-box' : '',
          ...componentProps,
          style: 'width:100%',
          value: myFormModalValue.value,
        };
        let on = {};
        if (component == 'Input') {
          on = {
            onChange: (e) => {
              myFormModalValue.value = e.target.value;
              emitChange();
            },
          };
        } else {
          on = {
            onChange: (val) => {
              myFormModalValue.value = val;
              emitChange();
            },
          };
        }
        const compAttr = {
          ...propsData,
        };

        return <Comp {...compAttr} {...on} />;
      }

      return () => {
        return renderComponent();
      };
    },
  });

  export default MyFormItem;
</script>
<style lang="less" scoped>
  :deep(.ant-select) {
    width: 100%;
  }
</style>
