<template>
  <div>
    <a-table
      :columns="columns"
      :bordered="showFormBorder"
      :pagination="false"
      :data-source="data"
      :scroll="{ x: 'max-content' }"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key !== 'action'">
          <template v-if="column.key === 'index'">
            {{ index + 1 }}
          </template>
          <FormItem
            :name="[subFormList, subFormIndex, mainKey, index, column.dataIndex]"
            :rules="rules(column, record, index)"
            :validateTrigger="['blur', 'change']"
            v-else
          >
            <!---如果是checked一类的组件-->
            <template v-if="checkedValueComponents.includes(column.componentType)">
              <component
                :is="componentMap.get(column.componentType)"
                :bordered="showComponentBorder"
                v-bind="getComponentsProps(column, record, index)"
                v-model:checked="record[column.dataIndex]"
              />
            </template>
            <!---如果是RangePicker组件-->
            <template
              v-else-if="
                column.componentType === 'RangePicker' || column.componentType === 'TimeRangePicker'
              "
            >
              <component
                :is="componentMap.get(column.componentType)"
                :bordered="showComponentBorder"
                v-bind="getComponentsProps(column, record, index)"
                v-model:startField="column.dataIndex.split(',')[0]"
                v-model:endField="column.dataIndex.split(',')[1]"
                v-model:value="record[column.dataIndex]"
                v-model:record="data[index]"
                :mainKey="mainKey"
              />
            </template>

            <!---如果是渲染函数组件-->
            <template v-else-if="column.componentType === 'Render'">
              <component
                :bordered="showComponentBorder"
                :is="
                  column.render({
                    model: record,
                    field: column.dataIndex,
                    rules: column.rules,

                    componentProps: getComponentsProps(column, record, index),
                  })
                "
              />
            </template>

            <template v-else-if="column.key !== 'index'">
              <component
                :is="componentMap.get(column.componentType)"
                :bordered="showComponentBorder"
                v-bind="getComponentsProps(column, record, index)"
                :index="index"
                :mainKey="mainKey"
                v-model:value="record[column.dataIndex]"
              />
            </template>
          </FormItem>
        </template>
        <template v-if="column.key === 'action' && !disabled">
          <MinusCircleOutlined @click="remove(index)" style="padding-bottom: 20px" />
        </template>
      </template>
    </a-table>
    <a-button type="dashed" block @click="add" v-if="!disabled">
      <PlusOutlined />
      {{ t('新增') }}
    </a-button>
  </div>
</template>
<script lang="ts" setup>
  import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons-vue';
  import { Form } from 'ant-design-vue';
  import { inject, onMounted, ref, unref, watch } from 'vue';
  import { SubFormColumn } from '../types';
  import { componentMap } from '../componentMap';
  import { DicDataComponents, checkedValueComponents, staticDataComponents } from '../helper';
  import { isFunction, cloneDeep, isBoolean, upperFirst } from 'lodash-es';
  import { deepMerge } from '/@/utils';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { FormActionType } from '/@/components/Form/src/types/form';

  const { t } = useI18n();
  // 用于包裹弹窗的form组件 因为一个FormItem 只能收集一个表单组件  所以弹窗的form 必须排除
  // const FormItemRest = Form.ItemRest;
  const FormItem = Form.Item;

  const emit = defineEmits(['change', 'update:value']);

  const props = defineProps({
    /**
     * 如果是编辑状态 默认现实的值
     */
    value: { type: Array as PropType<Recordable[]>, default: () => [] },
    /**
     * 需要绑定主表的字段  用于验证  必填
     */
    mainKey: { type: String, required: true },
    /**
     * 子表单配置
     */
    columns: {
      type: Array as PropType<SubFormColumn[]>,
      required: true,
    },
    /**
     * 是否禁用所有组件
     */
    disabled: {
      type: Boolean,
      default: false,
    },
    //是否显示表格边框
    showFormBorder: Boolean,
    //是否显示组件边框
    showComponentBorder: Boolean,
    //是否展示序号
    showIndex: Boolean,
    //add before hooks
    addBefore: Function,
    //add after hooks
    addAfter: Function,
    formApi: {
      type: Object as PropType<FormActionType>,
    },
    subFormIndex: Number,
    subFormList: String,
  });
  const data = ref<Recordable[]>([]);
  //缓存字段是否disable
  const cacheMap = new Map<String, number[]>();

  const columns = ref<SubFormColumn[]>(props.columns);
  // 注入表单数据
  const formModel = inject<any>('formModel', null);

  const { notification } = useMessage();

  onMounted(() => {
    data.value = cloneDeep(props.value);

    if (props.showIndex && columns.value && columns.value[0].key !== 'index') {
      columns.value.unshift({
        title: '序号',
        key: 'index',
        align: 'center',
        width: 60,
      });
    }
    columns.value = filterColum(columns.value);
  });

  watch(
    () => data.value,
    (val) => {
      emit('change', val);
      emit('update:value', val);
    },
    {
      deep: true,
    },
  );

  watch(
    () => props.columns,
    (val) => {
      columns.value = filterColum(val);
    },
  );

  watch(
    () => props.value,
    (v) => {
      data.value = v;

      //要保证在预加载之后在emit  不然预加载数据不会绑定到表单数据中
      emit('change', unref(data));
      emit('update:value', unref(data));
    },
    { deep: true },
  );

  const add = () => {
    //给各个组件赋默认值
    const pushObj: Recordable = {};

    //新增数据钩子
    if (isFunction(props.addBefore)) {
      props.addBefore(formModel, pushObj);
    }

    props.columns.forEach((column, index) => {
      //判断是否为操作菜单  并且设置了默认值
      if (column.key === 'index' && index === 0) return;
      if (column.key !== 'action') {
        if (column.componentType === 'RangePicker' || column.componentType === 'TimeRangePicker') {
          handleSetRangeTimeValue(pushObj, column.dataIndex as string);
        } else if (
          (column.componentType &&
            staticDataComponents.includes(column.componentType) &&
            (column.componentProps as any)?.datasourceType === 'staticData') ||
          (column.componentType &&
            DicDataComponents.includes(column.componentType) &&
            (column.componentProps as any)?.datasourceType === 'dic')
        ) {
          let { defaultSelect } = column.componentProps as any;
          pushObj[column!.dataIndex as string] = defaultSelect;
        } else {
          pushObj[column!.dataIndex as string] = column.defaultValue;
        }
      }
    });
    data.value.push(pushObj);
    emit('change', unref(data));
    emit('update:value', unref(data));
    //新增数据钩子
    if (isFunction(props.addAfter)) {
      props.addAfter(formModel, pushObj);
    }
  };

  const remove = (index) => {
    data.value.splice(index, 1);
    emit('change', unref(data));
    emit('update:value', unref(data));
  };
  /**
   * @author: tzx
   * @description: 设置RangeTime的值
   *
   */
  function handleSetRangeTimeValue(values: Recordable, field: string) {
    const startTimeKey = field.split(',')[0];
    const endTimeKey = field.split(',')[1];
    values[startTimeKey] = '';
    values[endTimeKey] = '';

    return values;
  }
  const rules = (column, record, index) => {
    if (column.key === 'index') return;
    const requiredRule = {
      required: getComponentsProps(column, record, index)?.required || false,
      message: `${column.title}是必填项`,
    };
    const rulesList = cloneDeep(getComponentsProps(column, record, index)?.rules);
    if (!rulesList) return [requiredRule];
    rulesList?.map((item) => (item.pattern = eval(item.pattern)));
    return [...rulesList, requiredRule];
  };

  const getComponentsProps = (column, record, index) => {
    let componentProps = cloneDeep(column?.componentProps);
    const dataIndex = column?.dataIndex;
    if (!componentProps) return;
    if (isFunction(componentProps)) {
      componentProps = componentProps({ updateSchema, formModel, record, index, disableRow }) ?? {};
    } else {
      if (componentProps['events']) {
        for (const eventKey in componentProps['events']) {
          try {
            const event = new Function(
              'schema',
              'formModel',
              'formActionType',
              `${componentProps['events'][eventKey]}`,
            );

            componentProps['on' + upperFirst(eventKey)] = function () {
              event(column, formModel, props.formApi);
            };
          } catch (error) {
            console.log('error', error);
            notification.error({
              message: 'Tip',
              description: '触发事件填写有误！',
            });
          }
        }
      }
    }
    if (isBoolean(props.disabled)) {
      componentProps['disabled'] = componentProps['disabled'] || props.disabled;
    } else if (isBoolean(componentProps['disabled'])) {
      componentProps['disabled'] =
        cacheMap.has(dataIndex) && cacheMap.get(dataIndex)?.includes(index) ? true : false;
    }

    return componentProps as Recordable;
  };

  const disableRow = (column: SubFormColumn, index?: number) => {
    let col: any = columns.value.find((x) => x.dataIndex == column.dataIndex);
    if (col && index !== undefined) {
      //如果当前字段已经缓存
      if (cacheMap.has(col.dataIndex)) {
        let indexArray = cacheMap.get(col.dataIndex);

        if (column.componentProps.disabled) {
          if (!indexArray) {
            indexArray = [index];
          } else {
            if (!indexArray.includes(index)) {
              indexArray.push(index);
            }
          }
        } else {
          if (indexArray) {
            if (indexArray.findIndex((x) => x === index) > -1) {
              indexArray.splice(
                indexArray.findIndex((x) => x === index),
                1,
              );
            }
          }
        }
      } else {
        if (column.componentProps.disabled) {
          cacheMap.set(col.dataIndex, [index]);
        }
      }
    }
  };

  const updateSchema = (column: SubFormColumn, index?: number) => {
    let col: any = columns.value.find((x) => x.dataIndex == column.dataIndex);
    if (col && index !== undefined) {
      //如果当前字段已经缓存
      if (cacheMap.has(col.dataIndex)) {
        let indexArray = cacheMap.get(col.dataIndex);

        if (column.componentProps.disabled) {
          if (!indexArray) {
            indexArray = [index];
          } else {
            indexArray.push(index);
          }
        } else {
          if (indexArray) {
            if (indexArray.findIndex((x) => x === index) > -1) {
              indexArray.splice(
                indexArray.findIndex((x) => x === index),
                1,
              );
            }
          }
        }
      } else {
        if (column.componentProps.disabled) {
          cacheMap.set(col.dataIndex, [index]);
        }
      }
    }
    return col || col?.length ? deepMerge(col, column) : col;
  };
  function filterColum(column) {
    return column?.filter((o) => {
      return (
        o.key == 'action' ||
        o.key == 'index' ||
        (((isBoolean(o.show) && o.show) || !isBoolean(o.show)) &&
          isBoolean(o.componentProps?.isShow) &&
          o.componentProps?.isShow) ||
        !isBoolean(o.componentProps?.isShow)
      );
    });
  }
</script>
<style lang="less" scoped>
  :deep(.ant-form-item) {
    margin-bottom: 0;
  }

  :deep(.ant-table-cell) {
    padding: 8px 0;
  }

  :deep(.ant-table .ant-table-thead tr th) {
    padding: 11px;
  }

  :deep(.ant-input-number),
  :deep(.ant-input-affix-wrapper),
  :deep(.ant-input),
  :deep(.ant-select-selector),
  :deep(.ant-picker) {
    border: 0 !important;
    box-shadow: none !important;
  }

  :deep(.anticon) {
    padding-bottom: 0 !important;
  }

  :deep(.ant-table-cell-fix-right) {
    text-align: center;
    width: 60px;
  }

  :deep(.ant-radio-group),
  :deep(.ant-checkbox-group),
  :deep(.ant-rate),
  :deep(.ant-upload),
  :deep(.ant-form-item-explain-error) {
    padding: 0 11px;
  }

  :deep(.ant-switch),
  :deep(input[type='color']) {
    margin: 0 11px;
  }

  .select-btn {
    margin-bottom: 8px;

    :deep(.ant-upload) {
      padding: 0;
    }
  }
</style>
