<template>
  <a-upload
    :file-list="fileList"
    :custom-request="$attrs['custom-request'] || customRequest"
    v-bind="omit($attrs, ['custom-request', 'onChange'])"
    @change="handleChange"
  >
    <template v-if="!$slots.default && fileList.length < $attrs['max-count']">
      <plus-outlined
        v-if="$attrs['list-type'] === 'picture' || $attrs['list-type'] === 'picture-card'"
      />
      <a-button v-else>点击上传</a-button>
    </template>
    <template v-for="(_, slot) in $slots" #[slot]="data" :key="slot">
      <slot :name="slot" v-bind="data"></slot>
    </template>
  </a-upload>
</template>
<script lang="ts" setup>
  import { omit } from 'lodash-es';
  import { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';
  import { PlusOutlined } from '@ant-design/icons-vue';
  import { uploadApi } from '/@/api/sys/upload';
  import { computed } from 'vue';

  defineOptions({
    name: 'Upload2',
    inheritAttrs: false,
  });

  const emit = defineEmits(['change', 'update:value']);

  const props = defineProps({
    value: {},
  });

  const fileList = computed(() => {
    return props.value || [];
  });

  const handleChange = ({ file: curFile, fileList }) => {
    const successStatus = ['uploading', 'done'];
    const newFileList = fileList
      .filter((file) => successStatus.includes(file.status))
      .map((file) => {
        const url = file.uid === curFile.uid ? curFile.xhr?.fileUrl : void 0;
        return { ...file, url };
      });

    emit('change', newFileList);
    emit('update:value', newFileList);
  };

  const customRequest = (request: UploadRequestOption) => {
    const { onSuccess, onError, file } = request;

    uploadApi({
      file: file,
    })
      .then((res) => {
        onSuccess && onSuccess(res, res);
      })
      .catch(() => {
        onError && onError(new Error('上传失败'));
      });
  };
</script>
<style lang="less" scoped></style>
