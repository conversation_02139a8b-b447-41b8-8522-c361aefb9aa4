import { ColumnProps } from 'ant-design-vue/lib/table/Column';
import { VNode } from 'vue';
import { FieldMapToTime, RenderCallbackParams, Rule } from './form';

type ColSpanType = number | string;
export interface ColEx {
  style?: any;
  /**
   * raster number of cells to occupy, 0 corresponds to display: none
   * @default none (0)
   * @type ColSpanType
   */
  span?: ColSpanType;

  /**
   * raster order, used in flex layout mode
   * @default 0
   * @type ColSpanType
   */
  order?: ColSpanType;

  /**
   * the layout fill of flex
   * @default none
   * @type ColSpanType
   */
  flex?: ColSpanType;

  /**
   * the number of cells to offset Col from the left
   * @default 0
   * @type ColSpanType
   */
  offset?: ColSpanType;

  /**
   * the number of cells that raster is moved to the right
   * @default 0
   * @type ColSpanType
   */
  push?: ColSpanType;

  /**
   * the number of cells that raster is moved to the left
   * @default 0
   * @type ColSpanType
   */
  pull?: ColSpanType;

  /**
   * <576px and also default setting, could be a span value or an object containing above props
   * @type { span: ColSpanType, offset: ColSpanType } | ColSpanType
   */
  xs?: { span: ColSpanType; offset: ColSpanType } | ColSpanType;

  /**
   * ≥576px, could be a span value or an object containing above props
   * @type { span: ColSpanType, offset: ColSpanType } | ColSpanType
   */
  sm?: { span: ColSpanType; offset: ColSpanType } | ColSpanType;

  /**
   * ≥768px, could be a span value or an object containing above props
   * @type { span: ColSpanType, offset: ColSpanType } | ColSpanType
   */
  md?: { span: ColSpanType; offset: ColSpanType } | ColSpanType;

  /**
   * ≥992px, could be a span value or an object containing above props
   * @type { span: ColSpanType, offset: ColSpanType } | ColSpanType
   */
  lg?: { span: ColSpanType; offset: ColSpanType } | ColSpanType;

  /**
   * ≥1200px, could be a span value or an object containing above props
   * @type { span: ColSpanType, offset: ColSpanType } | ColSpanType
   */
  xl?: { span: ColSpanType; offset: ColSpanType } | ColSpanType;

  /**
   * ≥1600px, could be a span value or an object containing above props
   * @type { span: ColSpanType, offset: ColSpanType } | ColSpanType
   */
  xxl?: { span: ColSpanType; offset: ColSpanType } | ColSpanType;
}

export type ComponentType =
  | 'Slot'
  | 'Input'
  | 'InputGroup'
  | 'InputPassword'
  | 'LabelComponent'
  | 'InputSearch'
  | 'InputTextArea'
  | 'InputNumber'
  | 'InputCountDown'
  | 'Select'
  | 'ApiSelect'
  | 'TreeSelect'
  | 'ApiTree'
  | 'ApiTreeSelect'
  | 'ApiRadioGroup'
  | 'RadioButtonGroup'
  | 'RadioGroup'
  | 'Checkbox'
  | 'CheckboxGroup'
  | 'ApiCheckboxGroup'
  | 'AutoComplete'
  | 'ApiCascader'
  | 'Cascader'
  | 'DatePicker'
  | 'MonthPicker'
  | 'RangePicker'
  | 'WeekPicker'
  | 'TimePicker'
  | 'TimeRangePicker'
  | 'Switch'
  | 'StrengthMeter'
  | 'Upload'
  | 'Upload2'
  | 'IconPicker'
  | 'Render'
  | 'Slider'
  | 'Rate'
  | 'Grid'
  | 'Tab'
  | 'Card'
  | 'ChildTable'
  | 'Divider'
  | 'Dept'
  | 'User'
  | 'Info'
  | 'Area'
  | 'SubForm'
  | 'DicSelect'
  | 'DbSelect'
  | 'MenuSelect'
  | 'DatasourceSelect'
  | 'DicItemSelect'
  | 'RichTextEditor'
  | 'Text'
  | 'Button'
  | 'MultiplePopup'
  | 'AssociateSelect'
  | 'Computation'
  | 'ColorPicker'
  | 'Title'
  | 'Opinion'
  | 'Image'
  | 'AutoCodeRule'
  | 'MoneyChineseInput'
  | 'XjrSelect'
  | 'SelectMap'
  | 'XjrQrcode'
  | 'OneForOne'
  | 'CodeTextArea'
  | 'SelectForm'
  | 'ErpApply'
  | 'ErpUpload'
  | 'ErpCheck'
  | 'FormView'
  | 'XjrIframe'
  | 'TableLayout'
  | 'TreeComponent'
  | 'TreeSelectComponent'
  | 'BarCode'
  | 'Signature'
  | 'SunForm'
  | 'File'
  | 'Signature'
  | 'NetworkSelect'
  | 'SelectDesignList';

/**
 * 子表单 列头配置 继承table的ColumnProps类型  新增的属性为当前列的组件配置
 */
export interface SubFormColumn extends ColumnProps {
  componentType?: ComponentType;
  rules?: Rule[];
  defaultValue?: any;
  fieldMapToTime?: FieldMapToTime; //RangerPick组件特有
  // Render the content in the form-item tag
  render?: (renderCallbackParams: RenderCallbackParams) => VNode | VNode[] | string;
  children?: SubFormColumn[];
  componentProps?: any;
  dataIndex?: string;
  title?: string;
  mainKey?: string;
}
