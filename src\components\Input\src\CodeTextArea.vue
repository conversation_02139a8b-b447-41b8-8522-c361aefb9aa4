<template>
  <div class="relative">
    <a-textarea
      v-model:value="value"
      :placeholder="placeholder"
      :maxlength="parseInt(maxlength)"
      :rows="rows"
      :disabled="disabled"
      :readonly="readonly"
      @change="handleChange"
      @blur="emit('blur')"
    />
    <!-- <a
      class="absolute bottom-0.25 right-2 bg-white left-1 text-right pb-1"
      href="https://liteflow.yomahub.com/pages/5816c5/"
      target="_blank"
      >{{ t('查看操作手册') }} <Icon icon="material-symbols:arrow-right-alt-rounded"
    /></a> -->
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch } from 'vue';
  // import { Icon } from '/@/components/Icon';
  // import { useI18n } from '/@/hooks/web/useI18n';
  // const { t } = useI18n();
  const props = defineProps({
    value: {
      type: String,
      default: '',
    },
    defaultValue: {
      type: String,
      default: '',
    },
    placeholder: String,
    maxlength: {
      type: [String, Number],
      default: '',
    },
    rows: Number,
    disabled: Boolean,
    readonly: <PERSON><PERSON><PERSON>,
    tips: String,
  });
  const value = ref('');
  const emit = defineEmits(['update:value', 'change', 'blur']);
  watch(
    () => props.value,
    (val) => {
      value.value = val;
    },
    {
      immediate: true,
    },
  );
  watch(
    () => props.defaultValue,
    (val) => {
      if (val) {
        emit('update:value', val);
      }
    },
    {
      immediate: true,
    },
  );
  const handleChange = (e) => {
    emit('update:value', e.target.value);
    emit('change', e);
    value.value = props.value === undefined ? e.target.value : props.value;
  };
</script>
<style scoped>
  textarea.ant-input {
    padding-bottom: 28px;
  }
</style>
