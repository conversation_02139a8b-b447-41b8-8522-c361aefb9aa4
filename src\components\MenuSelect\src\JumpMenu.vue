<template>
  <div>
    <TreeSelect
      v-model:value="selectData"
      style="width: 100%"
      :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
      :placeholder="placeholder"
      allow-clear
      tree-default-expand-all
      :tree-data="treeData"
      :field-names="{ children: 'children', label: 'title', value: 'id' }"
      @select="handleSelect"
    />
  </div>
</template>
<script lang="ts" setup>
  import { TreeSelect } from 'ant-design-vue';
  import { onMounted, ref } from 'vue';
  import { getJumpMenuMenuTree } from '/@/api/system/menu';
  const treeData = ref<Recordable[]>([]);
  const selectData = ref<string | undefined>('');
  const props = defineProps({
    value: String,
    placeholder: String,
  });

  const emit = defineEmits(['update:value', 'icon', 'name', 'path']);

  onMounted(() => {
    fetch();
  });

  async function fetch() {
    treeData.value = ((await getJumpMenuMenuTree()) as unknown as Recordable[]) || [];
    selectData.value = props.value;
  }
  function handleSelect(value, node) {
    console.log('node: ', node);
    emit('update:value', value);
    emit('icon', node.icon);
    emit('name', node.title);
    emit('path', node.path);
  }
</script>
