<template>
  <a-modal
    :visible="visible"
    :title="title"
    :maskClosable="false"
    :width="hasLeftSlot ? 1200 : width || 600"
    :okText="t('确定')"
    :cancelText="t('取消')"
    @ok="$emit('submit')"
    @cancel="$emit('close')"
  >
    <slot name="header"></slot>
    <div class="content">
      <div :class="['left', isDeptSelect ? 'left-box' : '']" v-if="hasLeftSlot">
        <slot name="left"></slot>
      </div>
      <div class="right">
        <slot></slot>
      </div>
    </div>
  </a-modal>
</template>
<script setup lang="ts">
  import { computed, useSlots } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  defineEmits(['submit', 'close']);
  defineProps({
    title: String,
    width: Number,
    visible: { type: Boolean, default: false },
    isDeptSelect: { type: Boolean, default: false },
  });
  const hasLeftSlot = computed(() => {
    return !!useSlots().left;
  });
</script>
<style lang="less" scoped>
  .content {
    height: 520px;
    display: flex;
    margin: 10px;
    overflow: auto;

    .left {
      flex-basis: 310px;
      margin-right: 10px;
    }

    .right {
      flex: 1;
    }
  }

  :deep(.search-box) {
    padding: 10px 20px;
  }

  :deep(.list-box) {
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    padding: 10px 0;
  }

  :deep(.list-page-box) {
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    padding: 10px 0;
  }

  :deep(.page-box) {
    position: absolute;
    bottom: 80px;
    right: 20px;
  }

  :deep(.ant-spin-nested-loading) {
    height: 480px;
    margin-top: 10px;
  }

  :deep(.title) {
    font-size: 16px;
    color: #333;
    padding: 0 0 10px 10px;
    margin: 0 20px;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.ant-checkbox-inner) {
    border-radius: 50%;
  }

  :deep(.ant-checkbox-checked .ant-checkbox-inner) {
    border-radius: 50%;
  }

  .left-box {
    margin-left: calc(50% - 155px);
  }
</style>
