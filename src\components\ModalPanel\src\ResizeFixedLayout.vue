<template>
  <div class="flow-containers">
    <div class="resize-head"><slot name="head"></slot></div>
    <div class="resize-layout" :id="layoutId">
      <div class="resize-left" ref="left"><slot name="left"></slot></div>
      <div class="resize-shrink-sidebar" :id="sidebarId" title="收缩侧边栏">
        <span class="shrink-sidebar-text" v-if="showPanel">⋮</span>
      </div>
      <div class="resize-right" ref="right">
        <div class="right-box">
          <slot name="right"></slot>
        </div>
      </div>
      <div class="fewer-panel-box" @click="changeShowPanel">
        <component :is="fewerPanelComponent" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, ref } from 'vue';
  import FewerLeft from './FewerLeft.vue';
  import FewerRight from './FewerRight.vue';
  import { randomNum } from '/@/views/workflow/design/bpmn/util/random';
  let left = ref();
  let right = ref();
  let showPanel = ref(true);
  let fewerPanelComponent = computed(() => {
    return showPanel.value ? FewerRight : FewerLeft;
  });
  let sidebarId = ref(randomNum());
  let layoutId = ref(randomNum());
  onMounted(() => {
    dragControllerDiv();
  });
  function changeShowPanel() {
    showPanel.value = !showPanel.value;
    if (showPanel.value) {
      showRightBox();
    } else {
      hideRightBox();
    }
  }
  function showRightBox() {
    left.value.style.width = '65%';
    right.value.style.width = '35%';
  }
  function hideRightBox() {
    left.value.style.width = '100%';
    right.value.style.width = '0';
  }
  function changeWidth(moveLen: number, boxClientWidth: number) {
    left.value.style.width = moveLen + 'px';
    right.value.style.width = boxClientWidth - moveLen - 10 + 'px';
  }
  function dragControllerDiv() {
    let resize = document.getElementById(sidebarId.value) as any;
    let box = document.getElementById(layoutId.value) as any;

    resize.onmousedown = function (e: { clientX: any }) {
      let startX = e.clientX;
      resize.left = resize.offsetLeft;
      document.onmousemove = function (e) {
        let endX = e.clientX;
        let moveLen = resize.left + (endX - startX);
        let maxT = box.clientWidth - resize.offsetWidth;

        if (moveLen < 32) moveLen = 32;
        if (moveLen > maxT - 400) moveLen = maxT - 400;

        resize.style.left = moveLen;
        changeWidth(moveLen, box.clientWidth);
      };
      document.onmouseup = function () {
        document.onmousemove = null;
        document.onmouseup = null;
        resize.releaseCapture && resize.releaseCapture();
      };
      resize.setCapture && resize.setCapture();
      return false;
    };
  }
</script>

<style scoped>
  .flow-containers {
    position: fixed;
    inset: 0;
    z-index: 999;
    background-color: #fff;
  }

  [data-theme='dark'] .flow-containers {
    background-color: #151515;
  }

  .head {
    height: 50px;
  }

  .resize-layout {
    display: flex;
    height: calc(100% - 50px);
    width: 100%;
  }

  .resize-left {
    width: 65%;
    padding: 0;
    overflow: hidden;
  }

  .resize-shrink-sidebar {
    cursor: col-resize;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .shrink-sidebar-text {
    border-radius: 4px;

    /* color: #fff; */
  }

  .shrink-sidebar:hover {
    color: #666;
  }

  .resize-right {
    width: 35%; /* 右侧初始化宽度 */
  }

  .right-box {
    height: 100%;
    box-shadow: -8px 2px 4px rgb(0 0 0 / 10%);
  }

  .right-box:hover {
    box-shadow: -4px 2px 4px rgb(0 0 0 / 10%);
    border-left: 8px solid #fff;
    margin-top: -2px;
    padding-top: 2px;
    margin-left: -8px;
  }

  [data-theme='dark'] .right-box:hover {
    border-left: 8px solid #151515;
  }

  .fewer-panel-box {
    width: 20px;
    position: fixed;
    top: 80px;
    right: 10px;
    z-index: 3;
  }
</style>
