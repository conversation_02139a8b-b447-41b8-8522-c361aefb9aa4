<template>
  <div class="search">
    <div
      v-for="(item, index) in searchData.list"
      :key="index"
      class="search-item"
      :style="item.type == 'date' ? 'flex-basis: 48%' : ''"
    >
      <!-- 搜索按钮 -->
      <div class="item" v-if="item.type == 'search'">
        <a-button type="primary" @click="search" class="search-btn">{{ t('搜索') }}</a-button>
        <a-button @click="reset" class="search-btn">{{ t('重置') }}</a-button>
        <span @click="expansionOrContraction" class="show-icon" v-if="searchData.showSearchIcon"
          >{{ searchData.showAll ? t('收缩') : t('展开') }}
          <Icon icon="ion:chevron-forward" :class="searchData.showAll ? 'show' : 'hide'"
        /></span>
      </div>
      <!-- 时间范围 -->
      <div class="item" v-else-if="item.type == 'date'">
        <div class="label" v-if="item.label">{{ item.label }}：</div>
        <a-range-picker
          v-model:value="searchData.searchForm[item.field]"
          :format="dateFormat"
          :valueFormat="dateFormat"
          style="width: 100%"
        />
      </div>
      <!-- 用户 -->
      <div class="item" v-else-if="item.type == 'user'">
        <div class="label" v-if="item.label">{{ item.label }}：</div>
        <SelectUser
          :selectedIds="searchData.searchForm[item.field]"
          :multiple="true"
          @change="
            (ids) => {
              searchData.searchForm[item.field] = ids;
            }
          "
          @change-names="
            (names) => {
              searchData.showData[item.field] = names;
            }
          "
        >
          <a-input
            :value="searchData.showData[item.field]"
            :placeholder="item.label"
            style="width: 100%"
          >
            <template #addonAfter>
              <Icon icon="ant-design:user-outlined" />
            </template>
          </a-input>
        </SelectUser>
      </div>

      <div class="item" v-else>
        <div class="label" v-if="item.label">{{ item.label }}：</div>
        <a-input
          v-model:value="searchData.searchForm[item.field]"
          :placeholder="t('请输入') + item.label"
          style="width: 100%"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive } from 'vue';
  import { Icon } from '/@/components/Icon/index';
  import { SelectUser } from '/@/components/SelectOrganizational/index';
  import { cloneDeep } from 'lodash-es';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const dateFormat = 'YYYY-MM-DD HH:mm:ss';
  const props = defineProps(['searchConfig']);
  const emits = defineEmits(['search', 'scrollHeight']);
  // 搜索
  onMounted(() => {
    initSearch();
  });
  let searchData: {
    showAll: boolean;
    showSearchIcon: boolean;
    searchForm: any;
    showData: any;
    originalData: Array<{ field: string; label: string; value: string; type: string }>;
    list: Array<{ field: string; label: string; value: string; type: string }>;
  } = reactive({
    showAll: false,
    showSearchIcon: true,
    originalData: [],
    list: [],
    searchForm: {},
    showData: {},
  });

  function initSearch() {
    if (props.searchConfig) {
      searchData.showAll = true;
      searchData.originalData = cloneDeep(props.searchConfig);
      searchData.originalData.forEach((element) => {
        if (element.field === 'originator' || element.field === 'userID') {
          searchData.searchForm[element.field] = [];
          searchData.showData[element.field] = '';
        } else if (element.field === 'searchDate') {
          searchData.searchForm[element.field] = [null, null];
        } else {
          searchData.searchForm[element.field] = element.value ? element.value : '';
        }
      });
      expansionOrContraction();
    }
  }
  function search() {
    let params = {};
    for (const key in searchData.searchForm) {
      if (Object.prototype.hasOwnProperty.call(searchData.searchForm, key)) {
        const value = searchData.searchForm[key];
        if (key === 'originator') {
          //发起人
          if (Array.isArray(value)) {
            params['originator'] = value.join(',');
          }
        } else if (key === 'searchDate') {
          if (Array.isArray(value)) {
            params['startTime'] = value[0];
            params['endTime'] = value[1];
          }
        } else {
          params[key] = value;
        }
      }
    }
    emits('search', params);
  }
  function reset() {
    initSearch();
    search();
  }
  // 展开或者收缩
  function expansionOrContraction() {
    searchData.showAll = !searchData.showAll;
    if (searchData.originalData.length > 3) {
      searchData.showSearchIcon = true;
      searchData.list = cloneDeep(searchData.originalData).splice(0, 3);
      searchData.list.push({
        field: '',
        label: '',
        type: 'search',
        value: '',
      });
      if (searchData.showAll) {
        searchData.list.push(
          ...cloneDeep(searchData.originalData).splice(3, searchData.originalData.length),
        );
      }
    } else {
      searchData.showSearchIcon = false;
      searchData.list = cloneDeep(searchData.originalData);
      searchData.list.push({
        field: '',
        label: '',
        type: 'search',
        value: '',
      });
    }
    emits('scrollHeight');
  }
</script>
<style lang="less" scoped>
  // 搜索
  .search {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: flex-end;

    .search-item {
      display: flex;
      align-items: center;
      padding: 0 4px;
    }

    .item {
      display: flex;
      align-items: center;
      width: 100%;
    }

    .search-btn {
      margin-left: 4px;
    }

    .show-icon {
      color: @primary-color;
      margin: 0 10px;
      align-self: center;
    }

    .show {
      transform: rotate(-90deg);
    }

    .hide {
      transform: rotate(90deg);
    }

    .label {
      min-width: 70px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
</style>
