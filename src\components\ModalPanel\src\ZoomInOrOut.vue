<template>
  <div class="fixed-bottom-item">
    <div class="fixed-common-btn" @click="$emit('out')">
      <IconFontSymbol icon="fangda" />
    </div>
    <div class="fixed-common-btn" @click="$emit('in')">
      <IconFontSymbol icon="suoxiao" />
    </div>
  </div>
</template>
<script lang="ts" setup>
  import IconFontSymbol from '/@/components/IconFontSymbol/Index.vue';
  defineEmits(['in', 'out']);
</script>
<style lang="less" scoped>
  .fixed-bottom-item {
    display: flex;
    box-shadow: 0px 2px 2px 2px rgb(0 0 0 / 4%);
    border-radius: 4px;
    padding: 4px;
    margin-right: 10px;
    height: 40px;
  }

  .fixed-common-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 6px;
    cursor: pointer;
    font-size: 24px;
  }
</style>
