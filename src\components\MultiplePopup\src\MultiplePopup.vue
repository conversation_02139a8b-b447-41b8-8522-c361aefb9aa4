<template>
  <div>
    <a-input
      v-model:value="popupValue"
      :placeholder="placeholder"
      :addonBefore="addonBefore"
      :addonAfter="addonAfter"
      :disabled="disabled"
      :bordered="bordered"
      autoComplete="off"
      :size="size"
      allowClear
      @click="showDialog"
      @change="handleChange"
    >
      <template #prefix v-if="prefix">
        <Icon :icon="prefix" />
      </template>
      <template #suffix v-if="suffix">
        <Icon :icon="suffix" />
      </template>
    </a-input>
    <FormItemRest>
      <MultipleSelect
        ref="MultipleSelectRef"
        v-model:multipleDialog="multipleDialog"
        :popupType="popupType"
        :dataSourceOptions="dataSourceOptions"
        :params="params"
        v-model:value="defaultVal"
        v-model:popupValue="popupValue"
        :labelField="labelField"
        :valueField="valueField"
        :datasourceType="datasourceType"
        :dicOptions="dicOptions"
        :apiConfig="apiConfig"
        v-model:selectedDataSource="selectedDataSourceVal"
        :mainKey="mainKey"
        :subTableIndex="index"
        @get-list="getList"
        @submit="handleSubmit"
      />
    </FormItemRest>
  </div>
</template>
<script lang="ts" setup>
  import { ref, watch, nextTick, inject, onMounted } from 'vue';
  import { Form } from 'ant-design-vue';
  import { Icon } from '/@/components/Icon';
  import MultipleSelect from './components/MultipleSelect.vue';
  import { camelCaseString, isValidJSON } from '/@/utils/event/design';

  const props = defineProps({
    defaultSelect: { type: String },
    popupType: { type: String },
    value: { type: String },
    labelField: { type: String, default: 'label' },
    valueField: { type: String, default: 'value' },
    placeholder: { type: String },
    addonBefore: { type: String },
    addonAfter: { type: String },
    prefix: { type: String },
    suffix: { type: String },
    disabled: { type: Boolean },
    params: { type: [Array, Object, String, Number] },
    dataSourceOptions: { type: Array },
    dicOptions: { type: Array },
    //数据来源 默认为空  如果不为空 则参数 api
    datasourceType: String,
    apiConfig: { type: Object },
    bordered: {
      type: Boolean,
      default: true,
    },
    mainKey: String,
    index: Number,
    size: String,
    //填表按钮绑定使用
    prestrainField: String,
  });
  const FormItemRest = Form.ItemRest;
  const formModel = inject<any>('formModel', null);
  const isCamelCase = inject<boolean>('isCamelCase', false);
  const multipleDialog = ref<boolean>(false);
  const popupValue = ref('');
  const defaultVal = ref<string | undefined>('');
  const selectedDataSourceVal = ref<any[]>([]);
  const MultipleSelectRef = ref();
  const dataSourceList = ref<string[]>([]);
  const isAdd = ref(false);

  const emit = defineEmits(['update:value', 'change']);

  watch(
    () => props.value,
    () => {
      nextTick(async () => {
        await getSubDatasourceList();
        if (props.defaultSelect && props.value === props.defaultSelect) {
          setFormModel();
        }
      });
    },
    {
      immediate: true,
    },
  );

  const getList = (list) => {
    dataSourceList.value = list;
  };

  const showDialog = () => {
    multipleDialog.value = true;
  };

  const handleSubmit = (saveValue) => {
    isAdd.value = true;
    emit('update:value', saveValue);
    emit('change');
  };

  const getSubDatasourceList = async () => {
    defaultVal.value = props.value;

    if (isAdd.value) return;
    popupValue.value = '';
    let showValueArr: string[] = [];
    await MultipleSelectRef.value?.getDatasourceList();
    selectedDataSourceVal.value = [];
    const selectedArr = props.value?.split(',');
    dataSourceList.value?.map((item) => {
      selectedArr?.map((selected) => {
        if (item[props.valueField] === selected) {
          selectedDataSourceVal.value?.push(item);
        }
      });
    });
    showValueArr = selectedDataSourceVal.value?.map((item: any) => {
      return item[props.labelField!];
    });
    await MultipleSelectRef.value?.setSelected(selectedDataSourceVal.value);

    popupValue.value = showValueArr.length ? showValueArr.join(',') : props.value!;
  };

  const handleChange = (e) => {
    if (!e.target.value) {
      emit('update:value', e.target.value);
      emit('change');
      MultipleSelectRef.value.setFormModel(true);
    }
  };
  onMounted(() => {
    if (props.datasourceType === 'api') {
      props.apiConfig?.apiParams?.forEach((params) => {
        params.tableInfo?.forEach((o) => {
          if (o.bindType == 'data') {
            let val = isValidJSON(o.value);
            let field = '';
            if (val && val.bindTable) {
              let table = !isCamelCase
                ? val.bindTable + 'List'
                : camelCaseString(val.bindTable + '_List');
              field = !isCamelCase ? val.bindField : camelCaseString(val.bindField);
              formModel &&
                formModel[table!][props.index || 0] &&
                formModel[table!][props.index || 0][field];
            } else if (val && val.bindField) {
              field = !isCamelCase ? val.bindField : camelCaseString(val.bindField);
              formModel && formModel[field];
            }
          }
        });
      });
      getSubDatasourceList();
    }
  });
  const setFormModel = (isNull?) => {
    if (props.popupType === 'associate') {
      let assoConfig;
      switch (props.datasourceType) {
        case 'datasource':
          assoConfig = props.dataSourceOptions;
          break;
        case 'dic':
          assoConfig = props.dicOptions;
          break;
        case 'api':
          assoConfig = props.apiConfig?.outputParams;
          break;
      }
      if (!formModel) return;

      assoConfig?.map((item: any) => {
        if (item.bindField) {
          let value = '';
          if (selectedDataSourceVal.value.length) {
            value = selectedDataSourceVal.value![0][item.name];
          } else if (popupValue.value) {
            const selected = dataSourceList.value.find(
              (x) => x[props.prestrainField!] === props.value,
            );
            if (selected) {
              value = selected[item.name];
            }
          }
          let bindField = !isCamelCase ? item.bindField : camelCaseString(item.bindField);
          let bindTable = '';
          if (item.bindTable) {
            bindTable = !isCamelCase
              ? item.bindTable + 'List'!
              : camelCaseString(item.bindTable + '_List')!;
          }
          let val = isNull ? '' : value;
          if (props.mainKey) {
            if (!item.bindTable) {
              formModel[bindField!] = val;
            } else {
              formModel[props.mainKey][props.index!][bindField!] = val;
            }
          } else {
            if (item.bindTable) {
              formModel[bindTable][0][bindField!] = val;
            } else {
              formModel[bindField!] = val;
            }
          }
        }
      });
    }
  };
</script>
