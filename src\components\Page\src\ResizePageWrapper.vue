<template>
  <PageWrapper dense contentFullHeight fixedHeight contentClass="flex">
    <div class="form-container !p-2 relative">
      <div class="form-left relative" :style="{ width: formLeft }" v-if="props.hasLeft">
        <slot name="resizeLeft"></slot>
      </div>
      <div
        class="resize-shrink-sidebar"
        id="resize-form-left"
        title="收缩侧边栏"
        @mousedown="handleResize"
        v-if="props.hasLeft"
      >
        <span class="shrink-sidebar-text">⋮</span>
      </div>
      <div class="form-right" :style="{ width: formRight }">
        <slot name="resizeRight"></slot>
      </div>
      <slot></slot>
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
  import { ref } from 'vue';
  import PageWrapper from './PageWrapper.vue';
  const props = defineProps({
    hasLeft: {
      type: Boolean,
      default: true,
    },
  });
  let formLeft = ref('220px');
  let formRight = ref('calc(100% - 220px)');

  function handleResize(e) {
    let resize = document.getElementById('resize-form-left') as any;
    let startX = e.clientX;
    let left = resize?.offsetLeft || 0;

    document.onmousemove = function (e) {
      let endX = e.clientX;
      let moveLen = left + (endX - startX);
      formLeft.value = moveLen + 'px';
      formRight.value = 'calc(100% - ' + moveLen + 'px)';
    };
    document.onmouseup = function () {
      document.onmousemove = null;
      document.onmouseup = null;
      resize.releaseCapture && resize.releaseCapture();
    };
  }
</script>

<style lang="less" scoped>
  .form-container {
    flex: 1;
    display: flex;
    width: 100%;
  }

  .resize-shrink-sidebar {
    cursor: col-resize;
    display: flex;
    justify-content: center;
    align-items: center;

    .shrink-sidebar-text {
      padding: 0 2px;
      background: #f2f2f2;
      border-radius: 10px;
    }
  }

  .form-left {
    width: 100%;
    height: calc(100vh - 140px);
    background-color: #fff;
  }

  .form-right {
    flex: 1;
    height: calc(100vh - 140px);
    background-color: #fff;
  }
</style>
