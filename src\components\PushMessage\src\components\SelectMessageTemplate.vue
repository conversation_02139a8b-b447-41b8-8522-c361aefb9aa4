<template>
  <div>
    <div class="form-box">
      <div class="item">
        <div class="label"><em class="text-red-600">*</em>{{ t('消息模板') }}：</div>
        <a-input
          :value="props.config.name || data.config.name"
          :placeholder="t('点击选择消息模板')"
          @click="open"
          style="width: 100%"
        >
          <template #suffix>
            <Icon icon="ant-design:ellipsis-outlined" />
          </template>
        </a-input>
      </div>
      <div class="item">
        <div class="label"><em class="text-red-600">*</em>{{ t('模板类型') }}：</div>
        <a-select
          :value="props.config.type || data.config.type"
          style="width: 100%"
          :options="messageTypeOptions"
          disabled
        />
      </div>
    </div>

    <a-modal
      :width="1200"
      v-model:visible="visible"
      :title="t('选择消息模板')"
      :maskClosable="false"
      @ok="handleSubmit"
      @cancel="handleClose"
    >
      <TemplateList v-if="visible" v-model:id="data.config.id" />
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref } from 'vue';
  import { Icon } from '/@/components/Icon';
  import TemplateList from './TemplateList.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { message } from 'ant-design-vue';
  import { MessageType } from '/@/enums/messageTemplate';
  import { MessageConfigure } from '../interface';
  import { getInfo } from '/@/api/system/generator/messageTemplate';
  const { t } = useI18n();
  const emit = defineEmits(['setConfig']);
  const props = withDefaults(defineProps<{ config: MessageConfigure }>(), {
    config: () => {
      return {
        id: '',
        name: '',
        type: MessageType.EMAIL,
        configs: [],
        receiverConfiguration: [],
      };
    },
  });
  const messageTypeOptions = [
    {
      value: MessageType.DING_DING,
      label: t('钉钉'),
    },
    {
      value: MessageType.EMAIL,
      label: t('邮箱'),
    },
    {
      value: MessageType.SMS,
      label: t('短信'),
    },
    {
      value: MessageType.WEB_HOOK,
      label: t('WebHook'),
    },
    {
      value: MessageType.WE_CHAT,
      label: t('微信公众号'),
    },
    {
      value: MessageType.WE_COM,
      label: t('企业微信'),
    },
    {
      value: MessageType.SYSTEM,
      label: t('系统消息'),
    },
  ];
  let data: { config: MessageConfigure } = reactive({
    config: {
      id: '',
      name: '',
      type: MessageType.EMAIL,
      configs: [],
      receiverConfiguration: [],
    },
  });
  const visible = ref<boolean>(false);
  onMounted(() => {
    if (props.config.id) {
      data.config.id = props.config.id;
      // getApiData();
    }
  });
  function open() {
    visible.value = true;
  }
  async function handleSubmit() {
    if (data.config.id) {
      getApiData();
      visible.value = false;
    } else {
      message.warning('请选择一个木模板');
    }
  }
  async function getApiData() {
    let res = await getInfo(data.config.id);
    if (res.id) data.config.id = res.id;
    if (res.name) data.config.name = res.name;
    data.config.type = res.messageType;
    emit('setConfig', res);
  }
  function handleClose() {
    data.config.id = props.config.id; //还原
    visible.value = false;
  }
</script>

<style lang="less" scoped>
  .title {
    display: flex;
    height: 40px;
    font-size: 16px;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
  }

  .form-box {
    display: flex;
    justify-content: space-around;
    margin-bottom: 10px;
  }

  .item {
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 1;
  }

  .label {
    width: 120px;
    margin-left: 20px;
  }

  .select-box {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border: 1px solid #d9d9d9;
    padding: 0 4px;
    height: 30px;
    width: 100%;
  }

  .rule-text {
    color: #d0cfd0;
  }

  .list {
    .row {
      height: 40px;
      line-height: 30px;
      display: flex;
      justify-content: space-around;
      align-items: center;

      span {
        display: flex;
        justify-content: center;
        align-items: center;
      }

      .common {
        flex-basis: 25%;
        margin-right: 4px;
      }
    }

    .head {
      background-color: #f9f9f9;
    }

    .item {
      border-bottom: 1px solid #f9f9f9;
    }

    .delete-icon {
      color: @clear-color;
    }
  }
</style>
