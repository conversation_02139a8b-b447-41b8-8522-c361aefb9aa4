<template>
  <div class="box">
    <div class="box-left">
      <div class="title">{{ t('模板类型') }}</div>
      <BasicTree
        title=""
        :clickRowToExpand="true"
        :treeData="data.treeData"
        :fieldNames="{ key: 'id', title: 'name' }"
        @select="handleSelect"
      />
    </div>
    <div class="box-right">
      <div class="title">{{ t('模板列表') }}</div>
      <a-row :gutter="12" style="padding: 10px 0; border-top: 1px solid #f0f0f0">
        <a-col :span="8">
          <a-input v-model:value="data.searchText" :placeholder="t('输入搜索关键字')" />
        </a-col>
        <a-col>
          <a-button type="primary" @click="handleSearch"> {{ t('搜索') }} </a-button>
          <a-button type="primary" @click="handleReset" class="ml-2"> {{ t('重置') }} </a-button>
        </a-col>
      </a-row>
      <a-table
        :dataSource="data.dataSource"
        :columns="data.configColumns"
        rowKey="id"
        :pagination="data.paginationProps"
        :row-selection="rowSelection"
        :customRow="customRow"
        :scroll="{ y: '200px' }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import { computed, onMounted, reactive } from 'vue';
  import { ColumnProps } from 'ant-design-vue/lib/table/Column';
  import { getPage } from '/@/api/system/generator/messageTemplate';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { BasicTree, TreeItem } from '/@/components/Tree';
  import { getDicDetailList } from '/@/api/system/dic';
  import { MessageTemplateCategory } from '/@/enums/messageTemplateEnum';

  const { t } = useI18n();
  const emit = defineEmits(['update:id']);
  const props = defineProps(['id']);
  const data = reactive({
    dataSource: [] as any[],
    treeData: [] as unknown as TreeItem[],
    selectedRowKeys: [] as string[],
    templateType: '',
    searchText: '' as string,
    configColumns: [
      {
        key: 'name',
        title: t('模板名称'),
        dataIndex: 'name',
        width: 300,
      },
      {
        key: 'messageTypeName',
        title: t('消息类型'),
        dataIndex: 'messageTypeName',
      },
    ] as ColumnProps[],
    paginationProps: {
      current: 1,
      total: 0,
      pageSize: 10,
      showQuickJumper: true,
      showSizeChanger: true,
      onChange: (page: number) => getInterfacePage(page),
      onShowSizeChange: (current: number, pageSize: number) => {
        data.paginationProps.pageSize = pageSize;
        getInterfacePage(current);
      },
    },
  });
  onMounted(() => {
    data.selectedRowKeys = [props.id];
    getTreeData();
    getInterfacePage(1);
  });
  const rowSelection = computed(() => {
    return {
      checkStrictly: true,
      type: 'radio',
      selectedRowKeys: data.selectedRowKeys,
      onChange: (selectedRowKeys: string[]) => {
        data.selectedRowKeys = selectedRowKeys;
        emit('update:id', selectedRowKeys[0]);
      },
    };
  });
  const customRow = (record) => {
    return {
      onClick: () => {
        let selectedRowKeys = [...data.selectedRowKeys];
        if (selectedRowKeys.indexOf(record.id) >= 0) {
          let index = selectedRowKeys.indexOf(record.id);
          selectedRowKeys.splice(index, 1);
        } else {
          selectedRowKeys = [record.id];
        }
        emit('update:id', selectedRowKeys[0]);
        data.selectedRowKeys = selectedRowKeys;
      },
    };
  };
  const getInterfacePage = async (page) => {
    data.paginationProps.current = page;
    const res = await getPage({
      keyword: data.searchText,
      size: data.paginationProps.pageSize,
      limit: data.paginationProps.current,
      templateType: data.templateType,
      enabledMark: 1,
    });
    data.dataSource = res.list;
  };
  const handleSearch = () => {
    getInterfacePage(1);
  };
  const handleReset = () => {
    data.searchText = '';
    getInterfacePage(1);
  };
  //获取左边树结构
  const getTreeData = async () => {
    let res = (await getDicDetailList({
      itemId: MessageTemplateCategory.ID,
    })) as unknown as TreeItem[];
    data.treeData = res;
  };
  function handleSelect(selectIds: Array<string>) {
    data.templateType = selectIds[0];
  }
</script>

<style scoped>
  .box {
    display: flex;
    padding: 10px;
  }

  .box-left {
    flex-basis: 20%;
  }

  .box-right {
    flex-basis: 80%;
  }

  .title {
    font-size: 16px;
    line-height: 18px;
    margin-bottom: 15px;
    padding-left: 6px;
    border-left: 6px solid #5e95ff;
  }

  :deep(.ant-table-body) {
    height: 200px;
  }
</style>
