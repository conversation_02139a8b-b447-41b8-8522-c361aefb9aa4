import { withInstall } from '/@/utils';

import organizationalTree from './src/OrganizationalTree.vue';
import userCard from './src/card/UserCard.vue';
import selectUser from './src/SelectUser.vue';
import selectPost from './src/SelectPost.vue';
import selectRole from './src/SelectRole.vue';
import selectMember from './src/SelectMember.vue';
import selectDepartment from './src/SelectDepartment.vue';

export const OrganizationalTree = withInstall(organizationalTree);
export const SelectUser = withInstall(selectUser);
export const SelectPost = withInstall(selectPost);
export const SelectRole = withInstall(selectRole);
export const UserCard = withInstall(userCard);
export const SelectMember = withInstall(selectMember);
export const SelectDepartment = withInstall(selectDepartment);
