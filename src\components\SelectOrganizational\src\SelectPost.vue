<template>
  <div style="width: 100%">
    <div @click="show"><slot></slot></div>

    <ModalPanel
      :visible="data.visible"
      :width="1200"
      :title="t('添加岗位')"
      @submit="submit"
      @close="close"
    >
      <template #left>
        <OrganizationalTree @select="handleSelect" />
      </template>
      <!-- 已选 -->
      <Selected type="post" :list="data.selectedList" @abolish="abolishChecked" />
      <SearchBox @search="search" />
      <div class="list-page-box" v-if="data.list.length > 0">
        <PostCard
          :class="data.selectedIds.includes(item.id) ? 'picked' : 'not-picked'"
          v-for="(item, index) in data.list"
          :key="index"
          :item="item"
          @click="checked(item)"
        >
          <template #check>
            <a-checkbox size="small" :checked="data.selectedIds.includes(item.id)" />
          </template>
        </PostCard>
        <div class="page-box">
          <a-pagination
            v-model:current="data.page.current"
            :pageSize="data.page.pageSize"
            :total="data.page.total"
            show-less-items
            @change="pageChange"
        /></div>
      </div>
      <EmptyBox v-if="data.list.length == 0" />
    </ModalPanel>
  </div>
</template>

<script setup lang="ts">
  import { reactive } from 'vue';
  import OrganizationalTree from './OrganizationalTree.vue';
  import PostCard from './card/PostCard.vue';
  import Selected from './Selected.vue';
  import { ModalPanel, EmptyBox, SearchBox } from '/@/components/ModalPanel/index';

  import { getPostTree, getPostMulti } from '/@/api/system/post';
  import { PostInfo } from '/@/api/system/post/model';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const emits = defineEmits(['change']);

  const props = withDefaults(
    defineProps<{
      selectedIds: Array<string>;
      multiple?: Boolean;
    }>(),
    {
      selectedIds: () => {
        return [];
      },
      disabledIds: () => {
        return [];
      },
    },
  );
  let data: {
    visible: boolean;
    multiSelect: boolean;
    page: { current: number; total: number; pageSize: number };
    list: Array<PostInfo>;
    selectedList: Array<PostInfo>;
    selectedIds: Array<string>;
    searchConfig: {
      keyword: string;
      deptId: string;
    };
  } = reactive({
    visible: false,
    multiSelect: false,
    page: {
      current: 1,
      total: 0,
      pageSize: 9,
    },
    selectedIds: [],
    list: [],
    selectedList: [],
    searchConfig: {
      keyword: '',
      deptId: '',
    },
  });

  async function show() {
    data.page.current = 1;
    data.page.total = 0;
    data.searchConfig.deptId = '';
    if (props.selectedIds && Array.isArray(props.selectedIds)) data.selectedIds = props.selectedIds;
    await getList();
    await getSelectedList();
    data.visible = true;
  }
  function submit() {
    emits('change', data.selectedIds);
    close();
  }
  function close() {
    data.list = [];
    data.selectedIds = [];
    data.selectedList = [];
    data.visible = false;
  }
  function checked(item) {
    if (props.multiple && props.multiple == true) {
      if (data.selectedIds.includes(item.id)) {
        data.selectedIds.splice(
          data.selectedIds.findIndex((itemId) => itemId === item.id),
          1,
        );
        data.selectedList.splice(
          data.selectedList.findIndex((ele) => ele.id === item.id),
          1,
        );
      } else {
        data.selectedIds.push(item.id);
        data.selectedList.push(item);
      }
    } else {
      if (data.selectedIds.includes(item.id)) {
        data.selectedIds = [];
        data.selectedList = [];
      } else {
        data.selectedIds = [item.id];
      }
    }
  }

  async function getList() {
    data.list = [];
    data.page.total = 0;
    let params = {
      limit: data.page.current,
      size: data.page.pageSize,
      departmentId: data.searchConfig.deptId,
      keyword: data.searchConfig.keyword,
    };
    let res = await getPostTree(params);
    if (res.total) {
      data.page.total = res.total;
    }
    if (res.list.length > 0) {
      res.list.forEach((ele) => {
        let item = {
          name: ele.name,
          id: ele.id,
          code: ele.code,
        };
        data.list.push(item);
      });
    }
  }
  function search(keyword: string) {
    data.page.current = 1;
    data.searchConfig.keyword = keyword;
    getList();
  }
  function handleSelect(deptId = '') {
    data.page.current = 1;
    data.searchConfig.deptId = deptId;
    getList();
  }
  function pageChange(pagination) {
    data.page.current = pagination.current;
  }
  async function getSelectedList() {
    let list = await getPostMulti(data.selectedIds.join(','));
    if (list.length > 0) {
      list.forEach((ele) => {
        data.selectedList.push({
          name: ele.name,
          id: ele.id,
          code: ele.code,
        });
      });
    }
  }
  function abolishChecked(id: string) {
    data.selectedList = data.selectedList.filter((ele) => {
      return ele.id != id;
    });
    data.selectedIds.splice(
      data.selectedIds.findIndex((itemId) => itemId === id),
      1,
    );
  }
</script>
