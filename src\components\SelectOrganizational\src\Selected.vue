<template>
  <div ref="drawer">
    <div class="flex justify-between mr-5">
      <NodeHead :node-name="listTitle" />
      <a-button @click="show">{{ title }}</a-button></div
    >
    <a-drawer
      v-if="data.visible"
      :getContainer="() => $refs.drawer"
      placement="top"
      :closable="false"
      :mask="false"
      :open="true"
      :visible="true"
    >
      <div class="selected-head title">
        <NodeHead :node-name="title" />
        <div class="close-icon" @click="close">+</div>
      </div>
      <div class="list-box" v-if="props.list && props.list.length > 0">
        <component
          :is="componentName"
          v-for="(item, index) in props.list"
          class="picked"
          :key="index"
          :item="item"
          @click="abolish(item.id)"
          :disabled="props.disabledIds && props.disabledIds.includes(item.id) ? true : false"
        >
          <template #check>
            <a-checkbox size="small" :checked="true" />
          </template>
        </component>
      </div>
      <EmptyBox v-else />
    </a-drawer>
  </div>
</template>

<script setup lang="ts">
  import { computed, reactive } from 'vue';
  import UserCard from './card/UserCard.vue';
  import RoleCard from './card/RoleCard.vue';
  import PostCard from './card/PostCard.vue';
  import { EmptyBox, NodeHead } from '/@/components/ModalPanel/index';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  const emits = defineEmits(['abolish']);

  const props = withDefaults(
    defineProps<{
      type: String;
      list: Array<{ id: string }>;
      disabledIds?: Array<string>;
    }>(),
    {
      type: () => {
        return '';
      },
      list: () => {
        return [];
      },
    },
  );

  let data = reactive({
    visible: false,
  });
  const componentName = computed(() => {
    if (props.type == 'user') {
      return UserCard;
    } else if (props.type == 'role') {
      return RoleCard;
    } else if (props.type == 'post') {
      return PostCard;
    } else {
      return UserCard;
    }
  });
  const title = computed(() => {
    if (props.type == 'user') {
      return t('已选用户');
    } else if (props.type == 'role') {
      return t('已选角色');
    } else if (props.type == 'post') {
      return t('已选岗位');
    } else {
      return '';
    }
  });
  const listTitle = computed(() => {
    if (props.type == 'user') {
      return t('用户列表');
    } else if (props.type == 'role') {
      return t('角色列表');
    } else if (props.type == 'post') {
      return t('岗位列表');
    } else {
      return '';
    }
  });
  async function show() {
    data.visible = true;
  }
  function close() {
    data.visible = false;
  }
  function abolish(id: string) {
    emits('abolish', id);
  }
</script>
<style scoped>
  .title {
    display: flex;
    justify-content: space-between;
    height: 40px;
    font-size: 16px;
    color: #333;
    border-bottom: 1px solid #f0f0f0;
  }

  :deep(.ant-drawer-content-wrapper) {
    width: 100% !important;
    box-shadow: 0 2px 2px 2px rgb(0 0 0 / 4%);
  }

  :deep(.ant-drawer-open) {
    position: absolute;
    width: calc(100% - 244px) !important;
    top: 50px;
    left: 240px;
    box-shadow: -5px 5px 4px 1px rgb(0 0 0 / 6%);
    height: calc(100% - 110px);
    z-index: 20;
  }

  .list-box {
    display: flex;
    flex-wrap: wrap;
    overflow-y: auto;
    padding: 10px 0;
  }

  .selected-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .selected-btn {
    position: absolute;
    right: 30px;
  }

  .close-icon {
    cursor: pointer;
    font-size: 24px;
    transform: rotate(45deg);
  }

  .picked {
    border-width: 1px;
    border-style: dotted;
  }
</style>
