<template>
  <div class="list-item">
    <div class="item-box">
      <div class="item-left"
        ><div class="icon-box"> <IconFontSymbol icon="zhiwei-copy" fill-color="#5e95ff" /></div
      ></div>
      <div class="item-box-box">
        <div class="item-form-name">{{ t('岗位名称') }}</div>
        <div class="item-title">{{ props.item?.name }}</div>
      </div>
      <div class="fixed-checked">
        <slot name="check"></slot>
      </div>
      <div class="fixed-icon">
        <IconFontSymbol icon="zhiwei1-copy" fill-color="#f8f8f8" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import IconFontSymbol from '/@/components/IconFontSymbol/Index.vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  const { t } = useI18n();
  let props = defineProps({
    item: Object,
  });
</script>

<style lang="less" scoped>
  @custom-color: #5e95ff;
  @bg-color: #ffffff;

  .list-item {
    width: 261px;
    height: 100px;
    background: @bg-color;
    border-color: transparent;
    border: 1px solid @custom-color;
    border-radius: 8px;
    margin-left: 20px;
    margin-bottom: 20px;
    overflow: hidden;

    &:hover {
      border: 1px solid @custom-color;
    }

    .item-box {
      display: flex;
      margin: 14px;
      position: relative;

      .item-left {
        .icon-box {
          width: 63px;
          height: 63px;
          font-size: 32px;
          border: 1px solid @custom-color;
          border-radius: 50%;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .fixed-checked {
        position: absolute;
        bottom: -20px;
        z-index: 1;
        right: -6px;
      }

      .fixed-icon {
        position: absolute;
        right: -44px;
        font-size: 99px;
        transform: rotate(-30deg);
        top: -44px;
      }
    }
  }

  :deep(.ant-checkbox-inner) {
    border-color: @custom-color;
  }

  :deep(.ant-checkbox-checked .ant-checkbox-inner) {
    background-color: @custom-color;
    border-color: @custom-color;
  }

  :deep(.ant-checkbox-checked::after),
  :deep(.ant-checkbox-wrapper:hover .ant-checkbox-inner, .ant-checkbox:hover),
  :deep(.ant-checkbox-inner),
  :deep(.ant-checkbox:hover),
  :deep(.ant-checkbox-input:focus + .ant-checkbox-inner) {
    border-color: @custom-color;
  }

  .picked {
    border-width: 1px;
    border-style: solid;
    border-color: @custom-color;
  }

  .not-picked {
    border-width: 1px;
    border-style: solid;
    border-color: #f3f3f3;
  }

  .item-box-box {
    position: absolute;
    z-index: 1;
    right: 80px;
    top: 0;
    display: flex;
    flex-direction: column;
  }

  .item-title {
    font-weight: 500;
    font-size: 14px;
    color: #636e80;
  }

  .item-form-name {
    font-weight: bold;
    font-size: 16px;
    color: #1d2129;
  }
</style>
