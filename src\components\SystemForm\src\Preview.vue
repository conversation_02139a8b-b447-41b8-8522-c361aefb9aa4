<template>
  <div v-if="visible" class="preview-box">
    <component
      :is="componentName"
      v-if="visible"
      ref="SystemForm"
      :fromPage="FromPageType.PREVIEW"
    />
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, computed, defineAsyncComponent } from 'vue';
  import { FromPageType } from '/@/enums/workflowEnum';
  const props = defineProps({
    systemComponent: {
      type: Object,
      default: () => {
        return {
          functionalModule: '',
          functionName: '',
          functionFormName: '',
        };
      },
    },
  });
  const visible = ref(false);
  const componentName = computed(() => {
    if (!props.systemComponent.functionName) {
      return defineAsyncComponent({
        loader: () => import('./Empty.vue'),
      });
    }
    return defineAsyncComponent({
      loader: () =>
        import(
          `./../../../views/${props.systemComponent.functionalModule}/${props.systemComponent.functionName}/components/Form.vue`
        ),
    });
  });

  onMounted(() => {
    visible.value = true;
  });
</script>

<style scoped>
  .preview-box {
    padding: 10px 20px;
  }
</style>
