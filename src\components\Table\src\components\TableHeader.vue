<template>
  <div style="width: 100%" :class="{ flex: toolBarWidth + formWidth + 200 < tableWidth }">
    <div v-if="$slots.headerTop" style="margin: 5px">
      <slot name="headerTop"></slot>
    </div>
    <div
      class="flex items-center justify-between table-header"
      :style="[toolBarWidth + formWidth + 200 < tableWidth ? 'flex:auto' : '']"
    >
      <slot name="tableTitle" v-if="$slots.tableTitle"></slot>
      <TableTitle
        :helpMessage="titleHelpMessage"
        :title="title"
        v-if="!$slots.tableTitle && title"
      />
      <div :class="['flex', advanceRight ? 'w-full' : '']">
        <!-- 是否配置高级搜索 -->
        <template v-if="isAdvancedQuery">
          <AdvancedQuery
            v-if="isAdvancedQuery"
            :querySelectOption="querySelectOption"
            :objectId="objectId"
            @submit="handleSearchInfoChange"
        /></template>
        <template v-else>
          <BasicForm
            ref="formRef"
            submitOnReset
            v-bind="{
              ...getFormProps,
              tableWidth,
              formWidth,
              isSearch: true,
              labelWidth,
            }"
            v-if="getBindValues.useSearchForm"
            :class="[advanceRight ? 'w-full !absolute top-0 left-0 z-50' : '']"
            :tableAction="tableAction"
            @register="registerForm"
            @submit="handleSearchInfoChange"
            @advanced-change="redoHeight"
            :slots="formSlots"
          >
            <template #[replaceFormSlotKey(item)]="data" v-for="item in Object.keys(formSlots)">
              <slot :name="item" v-bind="data || {}"></slot>
            </template>
          </BasicForm>
        </template>
      </div>
    </div>
    <div
      :class="`${prefixCls}__toolbar float-right`"
      :style="[toolBarWidth + formWidth + 200 < tableWidth ? 'flex: none' : 'margin: -5px 0 8px']"
    >
      <Divider type="vertical" v-if="$slots.toolbar" class="!ml-2 !mr-4" />
      <slot name="toolbar"></slot>
      <Divider type="vertical" v-if="$slots.toolbar && showTableSetting" class="!mx-3" />
      <TableSetting
        :setting="tableSetting"
        v-if="showTableSetting"
        @columns-change="handleColumnChange"
      />
    </div>
    <div v-if="$slots.headerContent">
      <slot name="headerContent"></slot>
    </div>
  </div>
</template>
<script lang="ts">
  import type { TableSetting, ColumnChangeParam, TableActionType } from '../types/table';
  import type { PropType } from 'vue';
  import { defineComponent, ref, computed, watch, defineAsyncComponent } from 'vue';
  import { Divider } from 'ant-design-vue';
  import TableSettingComponent from './settings/index.vue';
  import TableTitle from './TableTitle.vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  const BasicForm = defineAsyncComponent(() => import('/@/components/Form/src/BasicForm.vue'));
  const AdvancedQuery = defineAsyncComponent(() => import('./advancedQuery/Index.vue'));
  export default defineComponent({
    name: 'BasicTableHeader',
    components: {
      Divider,
      TableTitle,
      TableSetting: TableSettingComponent,
      BasicForm,
      AdvancedQuery,
    },
    props: {
      title: {
        type: [Function, String] as PropType<string | ((data: Recordable) => string)>,
      },
      tableSetting: {
        type: Object as PropType<TableSetting>,
      },
      showTableSetting: {
        type: Boolean,
      },
      objectId: {
        //系统表单formId,自定义表单releaseId的id值
        type: String,
        default: '',
      },
      isAdvancedQuery: {
        //是否高级查询
        type: Boolean,
        default: false,
      },
      querySelectOption: {
        //查询所有配置
        type: String,
        default: '',
      },
      titleHelpMessage: {
        type: [String, Array] as PropType<string | string[]>,
        default: '',
      },
      toolBarWidth: {
        type: Number,
        default: 0,
      },
      tableWidth: {
        type: Number,
        default: 0,
      },
      getFormProps: {
        type: Object,
        default: () => {},
      },
      getBindValues: {
        type: Object,
        default: () => {},
      },
      tableAction: {
        type: Object as PropType<TableActionType>,
        default: () => {},
      },
      handleSearchInfoChange: {
        type: Function,
        default: () => {},
      },
      redoHeight: {
        type: Function,
        default: () => {},
      },
      getFormSlotKeys: {
        type: [String],
      },
      replaceFormSlotKey: {
        type: Function,
        default: () => {},
      },
      registerForm: {
        type: Function,
        default: () => {},
      },
      slots: Object,
    },
    emits: ['columns-change'],
    setup(props, { emit }) {
      const { prefixCls } = useDesign('basic-table-header');
      const formRef = ref();
      const formWidth = ref<number>(0);
      const labelWidth = ref<number>(100);
      const schemas = ref([]);
      watch(
        () => props.getFormProps?.schemas,
        (val) => {
          if (val && Object.keys(val).length) {
            formWidth.value = val.length * 280 + formRef.value?.actionWidth;
            const notHasLonglabel = val.every((x) => {
              return x.label.length <= 4;
            });
            labelWidth.value = notHasLonglabel ? 75 : 100;
          }
          schemas.value = val;
        },
        {
          immediate: true,
          deep: true,
        },
      );

      const advanceRight = computed(() => {
        if (!formRef.value) return false;
        let advanceState = formRef.value!.advanceState;
        if (advanceState.isAdvanced && !advanceState.hideAdvanceBtn) {
          return true;
        } else {
          return false;
        }
      });
      function handleColumnChange(data: ColumnChangeParam[]) {
        emit('columns-change', data);
      }

      const formSlots = {};

      Object.keys(props.slots).forEach((key) => {
        if (key.startsWith('form-')) {
          formSlots[key] = props.slots[key];
        }
      });
      return {
        prefixCls,
        handleColumnChange,
        advanceRight,
        formRef,
        formWidth,
        labelWidth,
        formSlots,
        schemas,
      };
    },
  });
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-basic-table-header';
  @prefix-form: ~'@{namespace}-basic-form';

  :deep(.@{prefix-cls}) {
    &__toolbar {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      > * {
        margin-right: 4px;
      }
    }
  }

  :deep(.@{prefix-form}) {
    &--compact {
      .ant-form-item {
        margin-bottom: 0 !important;

        &-control-input-content {
          display: flex;
          align-items: center;

          > div,
          .input-content-box {
            width: 100%;
          }
        }
      }

      .suspensionRow .ant-form-item {
        margin-bottom: 8px !important;
      }

      > div {
        flex-wrap: nowrap;
      }
    }
  }

  .table-header :deep(.ant-col-8) {
    flex: auto;
    max-width: 280px;
    width: 280px;
    padding: 0 0 0 8px !important;
  }
</style>
