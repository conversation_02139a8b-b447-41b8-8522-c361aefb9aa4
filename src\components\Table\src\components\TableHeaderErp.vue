<template>
  <div style="width: 100%">
    <slot name="tableTitle" v-if="$slots.tableTitle"></slot>
    <div class="headerTitle" :helpMessage="titleHelpMessage" v-if="!$slots.tableTitle && title">{{
      title
    }}</div>
    <div v-if="$slots.headerTop" style="margin: 5px">
      <slot name="headerTop"></slot>
    </div>
    <div class="flex items-center justify-end h-10">
      <div :class="`${prefixCls}__toolbarBox`">
        <div :class="`${prefixCls}__toolbar`">
          <slot name="toolbar"></slot>
          <Divider type="vertical" v-if="$slots.toolbar && showTableSetting" />
          <TableSetting
            :setting="tableSetting"
            v-if="showTableSetting"
            @columns-change="handleColumnChange"
          />
        </div>
      </div>
    </div>
    <div v-if="$slots.headerContent">
      <slot name="headerContent"></slot>
    </div>
  </div>
</template>
<script lang="ts">
  import type { TableSetting, ColumnChangeParam } from '../types/table';
  import type { PropType } from 'vue';
  import { defineComponent } from 'vue';
  import { Divider } from 'ant-design-vue';
  import TableSettingComponent from './settings/index.vue';
  import { useDesign } from '/@/hooks/web/useDesign';

  export default defineComponent({
    name: 'BasicTableHeader',
    components: {
      Divider,
      TableSetting: TableSettingComponent,
    },
    props: {
      title: {
        type: [Function, String] as PropType<string | ((data: Recordable) => string)>,
      },
      tableSetting: {
        type: Object as PropType<TableSetting>,
      },
      showTableSetting: {
        type: Boolean,
      },
      titleHelpMessage: {
        type: [String, Array] as PropType<string | string[]>,
        default: '',
      },
    },
    emits: ['columns-change'],
    setup(_, { emit }) {
      const { prefixCls } = useDesign('basic-table-header');
      function handleColumnChange(data: ColumnChangeParam[]) {
        emit('columns-change', data);
      }
      return { prefixCls, handleColumnChange };
    },
  });
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-basic-table-header';

  .@{prefix-cls} {
    &__toolbar {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      padding-top: 8px;

      > * {
        margin-right: 8px;
      }
    }
  }
</style>
<style scoped>
  .headerTitle {
    align-items: center;
    color: #262626;
    font-size: 16px;
    padding: 6px 8px 38px;
    border-bottom: 1px solid #d9d9d9;
    height: 30px;
    line-height: 30px;
  }
</style>
