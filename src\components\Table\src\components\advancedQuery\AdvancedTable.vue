<template>
  <div v-if="show">
    <a-table :dataSource="state.dataSource" :columns="searchColumns" :pagination="false">
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'order'">
          <span>
            {{ index + 1 }}
          </span>
        </template>
        <template v-if="column.key === 'fieldName'">
          <a-select
            v-model:value="record[column.dataIndex]"
            style="width: 100%"
            :options="state.querySelectOption"
            :placeholder="t('请选择查询项')"
            :field-names="{ label: 'label', value: 'field', options: 'children' }"
            @change="
              (val) => {
                if (
                  state.querySelectOption &&
                  Array.isArray(state.querySelectOption) &&
                  state.querySelectOption.length > 0
                ) {
                  let arr = state.querySelectOption.filter(function (elem) {
                    return val == elem.field;
                  });
                  if (arr.length > 0) {
                    record.schemas = arr[0];
                  }
                }
              }
            "
          />
        </template>
        <template v-if="column.key === 'conditionType'">
          <a-select
            v-model:value="record[column.dataIndex]"
            style="width: 100%"
            :options="conditionTypeArray"
          />
        </template>
        <template v-if="column.key === 'fieldType'">
          <a-select
            v-model:value="record[column.dataIndex]"
            style="width: 100%"
            :options="fieldTypeArray"
          />
        </template>
        <template v-if="column.key === 'fieldValue'">
          <SearchFormItem
            v-if="
              record.schemas &&
              (record.fieldType == 0 || record.fieldType == 1 || record.fieldType == 8)
            "
            :schema="record.schemas"
            :formProps="{}"
            :value="record.fieldValue"
            @change="
              (val) => {
                record.fieldValue = val;
              }
            "
          />
          <div v-else>
            <div v-if="record['fieldType'] >= 2" class="content-flex">--</div>
            <span v-else>{{ getFieldTypeName(record.fieldType) }}</span>
          </div>
        </template>
        <template v-if="column.key === 'action'">
          <DeleteOutlined class="delete-box" @click="deleteAdd(index)"
        /></template>
      </template>
    </a-table>

    <a-button type="dashed" block @click="queryAdd">
      <PlusOutlined />
      {{ t('新增') }}
    </a-button>
    <div class="comm-box">
      <div class="label">组合公式 : </div>
      <div class="value">
        <a-input
          v-model:value="state.combinationFormula"
          placeholder="请填写组合公式,组合公式非必填，如果不填写，则按照已有条件并列执行，例如：1 and 2 and 3 and 4,填写后，按照填写的公式进行执行"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive, defineAsyncComponent, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  const SearchFormItem = defineAsyncComponent(
    () => import('/@/components/Form/src/components/SearchFormItem.vue'),
  );
  const { t } = useI18n();
  const show = ref(false);
  const props = defineProps({
    querySelectOption: {
      type: String,
      default: '',
    },
    configJson: {
      type: String,
      default: '',
    },
    combinationFormula: {
      type: String,
      default: '',
    },
  });
  const searchColumns = [
    {
      title: t('序号'),
      dataIndex: 'order',
      key: 'order',
      width: 60,
      align: 'center',
    },
    {
      title: '字段名',
      dataIndex: 'fieldName',
      key: 'fieldName',
      width: 80,
    },
    {
      title: '条件',
      dataIndex: 'conditionType',
      key: 'conditionType',
      width: 160,
    },
    {
      title: '字段类型',
      dataIndex: 'fieldType',
      key: 'fieldType',
      width: 120,
    },
    {
      title: '字段值',
      dataIndex: 'fieldValue',
      key: 'fieldValue',
      width: 240,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 60,
    },
  ];
  const conditionTypeArray = [
    {
      value: 0,
      label: t('等于'),
    },
    {
      value: 1,
      label: t('大于'),
    },
    {
      value: 2,
      label: t('大于等于'),
    },
    {
      value: 3,
      label: t('小于'),
    },
    {
      value: 4,
      label: t('小于等于'),
    },
    {
      value: 5,
      label: t('包含'),
    },
    // {
    //   value: 6,
    //   label: t('包含于'),
    // },
    {
      value: 7,
      label: t('不等于'),
    },
    {
      value: 8,
      label: t('不包含'),
    },
    // {
    //   value: 9,
    //   label: t('不包含于'),
    // },
  ];
  const fieldTypeArray = [
    {
      value: 0,
      label: t('文本(String)'),
    },
    {
      value: 1,
      label: t('文本(Int)'),
    },
    {
      value: 8,
      label: t('文本(Datetime)'),
    },
    {
      value: 2,
      label: t('登录人ID'),
    },
    {
      value: 3,
      label: t('登录人组织架构'),
    },
    {
      value: 4,
      label: t('登录人所属组织架构及下属组织架构'),
    },
    {
      value: 5,
      label: t('登录人账号'),
    },
    {
      value: 6,
      label: t('登录人岗位'),
    },
    {
      value: 7,
      label: t('登录人角色'),
    },
  ];
  const state = reactive({
    querySelectOption: [] as Array<{
      field: string;
    }>,
    combinationFormula: '',
    dataSource: [] as Array<{
      fieldName: string;
      fieldValue: string;
      schemas: any;
      conditionType: number;
      fieldType: number;
    }>,
  });

  onMounted(async () => {
    if (props.querySelectOption) {
      let val = JSON.parse(props.querySelectOption);
      state.querySelectOption = val;
    }
    if (props.configJson) {
      state.dataSource = JSON.parse(props.configJson);
    }
    if (props.combinationFormula) {
      state.combinationFormula = props.combinationFormula;
    }
    if (state.dataSource.length == 0) {
      let fieldName = '';
      let schemas = null;
      if (state.querySelectOption.length > 0 && state.querySelectOption[0]) {
        fieldName = state.querySelectOption[0].field;
        // @ts-ignore
        schemas = state.querySelectOption[0];
      }

      state.dataSource.push({
        fieldName: fieldName,
        fieldValue: '',
        schemas: schemas,
        conditionType: 0,
        fieldType: 0,
      });
    }
    show.value = true;
  });

  function queryAdd() {
    state.dataSource.push({
      fieldName: '',
      fieldValue: '',
      schemas: null,
      conditionType: 0,
      fieldType: 0,
    });
  }
  function deleteAdd(index) {
    state.dataSource.splice(index, 1);
  }
  function getDataSource() {
    return state.dataSource;
  }
  function getCombinationFormula() {
    return state.combinationFormula;
  }
  function getFieldTypeName(value) {
    if (value != 0 && value != 1 && value != 8) {
      let arr = fieldTypeArray.filter((ele) => {
        return ele.value == value;
      });
      return arr[0].label;
    } else {
      return t('请选择查询值');
    }
  }
  defineExpose({
    getDataSource,
    getCombinationFormula,
  });
</script>

<style lang="less" scoped>
  :deep(.ant-spin-nested-loading) {
    height: auto;
    margin-top: 10px;
    max-height: 700px;
  }

  .delete-box {
    font-size: 18px;
    font-weight: 700;
    color: #ee8d96;
    cursor: pointer;
  }

  .comm-box {
    display: flex;
    align-items: center;
    margin: 10px;

    .label {
      width: 70px;
      margin-right: 10px;
    }

    .value {
      flex: 1;
    }
  }

  .content-flex {
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
