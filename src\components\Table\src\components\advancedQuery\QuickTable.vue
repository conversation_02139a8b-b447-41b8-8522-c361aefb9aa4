<template>
  <div v-if="show">
    <a-table
      :dataSource="state.dataSource"
      :columns="searchColumns"
      :pagination="false"
      :key="renderKey"
    >
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'order'">
          <span>
            {{ index + 1 }}
          </span>
        </template>
        <template v-if="column.key === 'fieldName'">
          <a-select
            v-model:value="record[column.dataIndex]"
            style="width: 100%"
            :options="state.querySelectOption"
            :placeholder="t('请选择查询项')"
            :field-names="{ label: 'label', value: 'field', options: 'children' }"
            @change="
              (val) => {
                if (
                  state.querySelectOption &&
                  Array.isArray(state.querySelectOption) &&
                  state.querySelectOption.length > 0
                ) {
                  let arr = state.querySelectOption.filter(function (elem) {
                    return val == elem.field;
                  });
                  if (arr.length > 0) {
                    record.schemas = arr[0];
                  }
                }
              }
            "
          />
        </template>
        <template v-if="column.key === 'fieldValue'">
          <SearchFormItem
            v-if="record.schemas"
            :schema="record.schemas"
            :formProps="{}"
            :value="record.fieldValue"
            @change="
              (val) => {
                record.fieldValue = val;
              }
            "
          />
          <span v-else>请先选择查询项</span>
        </template>
        <template v-if="column.key === 'action'">
          <DeleteOutlined class="delete-box" @click="deleteAdd(index)"
        /></template>
      </template>
    </a-table>
    <a-button type="dashed" block @click="queryAdd">
      <PlusOutlined />
      {{ t('新增') }}
    </a-button>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, reactive, ref } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
  import SearchFormItem from '/@/components/Form/src/components/SearchFormItem.vue';
  const renderKey = ref(0);
  const { t } = useI18n();
  const show = ref(false);
  const props = defineProps({
    querySelectOption: {
      type: String,
      default: '',
    },
    configJson: {
      type: String,
      default: '',
    },
  });
  const searchColumns = [
    {
      title: t('序号'),
      dataIndex: 'order',
      key: 'order',
      width: 60,
      align: 'center',
    },
    {
      title: '查询条件',
      dataIndex: 'fieldName',
      key: 'fieldName',
      width: 160,
    },
    {
      title: '查询值或选项',
      dataIndex: 'fieldValue',
      key: 'fieldValue',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      width: 60,
    },
  ];

  const state = reactive({
    querySelectOption: [] as Array<{
      field: string;
    }>,
    dataSource: [] as Array<{
      fieldName: string;
      fieldValue: string;
      schemas: any;
    }>,
  });

  onMounted(async () => {
    if (props.querySelectOption) {
      let val = JSON.parse(props.querySelectOption);
      state.querySelectOption = val;
    }
    if (props.configJson) {
      state.dataSource = JSON.parse(props.configJson);
    }
    if (state.dataSource.length == 0) {
      let fieldName = '';
      let schemas = null;
      if (state.querySelectOption.length > 0 && state.querySelectOption[0]) {
        fieldName = state.querySelectOption[0].field;
        // @ts-ignore
        schemas = state.querySelectOption[0];
      }
      state.dataSource.push({
        fieldName: fieldName,
        fieldValue: '',
        schemas: schemas,
      });
    }
    show.value = true;
  });

  function queryAdd() {
    state.dataSource.push({
      fieldName: '',
      fieldValue: '',
      schemas: null,
    });
  }
  function deleteAdd(index) {
    state.dataSource.splice(index, 1);
    renderKey.value++;
  }
  function getDataSource() {
    return state.dataSource;
  }
  defineExpose({
    getDataSource,
  });
</script>

<style lang="less" scoped>
  :deep(.ant-spin-nested-loading) {
    height: auto;
    margin-top: 10px;
    max-height: 700px;
  }

  .delete-box {
    font-size: 18px;
    font-weight: 700;
    color: #ee8d96;
    cursor: pointer;
  }
</style>
