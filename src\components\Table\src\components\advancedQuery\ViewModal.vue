<template>
  <a-form
    :model="formState"
    name="basic"
    :label-col="{ span: 4 }"
    :wrapper-col="{ span: 20 }"
    autocomplete="off"
    ref="FormRef"
  >
    <a-row>
      <a-col class="import-title">基础信息</a-col>
    </a-row>
    <a-row>
      <a-col class="state" :span="12">
        <a-form-item
          label="视图编码"
          name="code"
          :rules="[{ required: true, message: '请填写视图编码!' }]"
        >
          <a-input v-model:value="formState.code" placeholder="请填写" />
        </a-form-item>
      </a-col>
      <a-col class="state" :span="12">
        <a-form-item
          label="视图名称"
          name="name"
          :rules="[{ required: true, message: '请填写视图名称!' }]"
        >
          <a-input v-model:value="formState.name" placeholder="请填写" />
        </a-form-item>
      </a-col>
      <a-col class="state" :span="12">
        <a-form-item label="可见范围" name="isPublic">
          <a-radio-group v-model:value="formState.isPublic" name="radioGroup">
            <a-radio :value="IS_PUBLIC.PUBLIC">公用</a-radio>
            <a-radio :value="IS_PUBLIC.PRIVATE">个人</a-radio>
          </a-radio-group>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
  <a-row>
    <a-col class="import-title">查询配置</a-col>
  </a-row>
  <div class="config-box">
    <a-tabs v-model:activeKey="formState.queryType" @change="changeQueryType">
      <a-tab-pane :key="QUERY_TYPE.QUICK_SEARCH" tab="快捷查询">
        <QuickTable
          ref="QuickTableRef"
          :querySelectOption="props.querySelectOption"
          :configJson="formState.configJson"
        />
      </a-tab-pane>
      <a-tab-pane :key="QUERY_TYPE.ADVANCED_QUERY" tab="高级查询">
        <AdvancedTable
          ref="AdvancedTableRef"
          :querySelectOption="props.querySelectOption"
          :configJson="formState.configJson"
          :combinationFormula="formState.combinationFormula"
        />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script setup lang="ts">
  import { onMounted, inject, reactive, ref, defineAsyncComponent } from 'vue';
  import { useI18n } from '/@/hooks/web/useI18n';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { IS_PUBLIC, QUERY_TYPE } from '/@/enums/formViewEnum';
  import { FormType } from '/@/enums/workflowEnum';
  const QuickTable = defineAsyncComponent(() => import('./QuickTable.vue'));
  const AdvancedTable = defineAsyncComponent(() => import('./AdvancedTable.vue'));
  const isCustomForm = inject('isCustomForm');
  const QuickTableRef = ref();
  const AdvancedTableRef = ref();
  const { t } = useI18n();
  const { notification } = useMessage();
  const FormRef = ref();
  const props = defineProps({
    querySelectOption: {
      type: String,
      default: '',
    },
    objectId: {
      type: String,
      default: '',
    },
    editData: {
      type: Object,
      default: () => {
        return {};
      },
    },
  });
  const formState = reactive({
    isPublic: IS_PUBLIC.PRIVATE,
    id: '',
    code: '',
    name: '',
    formViewType: FormType.CUSTOM, //表单类型
    configJson: '',
    combinationFormula: '',
    objectId: '', //系统表单formId,自定义表单releaseId的id值
    queryType: QUERY_TYPE.QUICK_SEARCH,
  });

  onMounted(async () => {
    formState.id = '';
    if (isCustomForm) {
      formState.formViewType = FormType.CUSTOM;
    } else {
      formState.formViewType = FormType.SYSTEM;
    }
    formState.objectId = props.objectId;
    if (props.editData && JSON.stringify(props.editData) != '{}') {
      formState.isPublic = props.editData.isPublic;
      if (props.editData.id) formState.id = props.editData.id;
      if (props.editData.name) formState.name = props.editData.name;
      if (props.editData.code) formState.code = props.editData.code;
      if (props.editData.configJson) formState.configJson = props.editData.configJson;
      if (props.editData.combinationFormula)
        formState.combinationFormula = props.editData.combinationFormula;
      formState.queryType = props.editData.queryType;
    }
  });

  async function handleSubmit() {
    if (formState.queryType == QUERY_TYPE.QUICK_SEARCH) {
      let arr = QuickTableRef.value.getDataSource();
      formState.configJson = JSON.stringify(arr);
    } else {
      let arr = AdvancedTableRef.value.getDataSource();
      formState.configJson = JSON.stringify(arr);
      let combinationFormula = AdvancedTableRef.value.getCombinationFormula();
      formState.combinationFormula = combinationFormula;
    }
    try {
      await FormRef.value.validate();
      return formState;
    } catch (error) {
      notification.error({
        message: t('提示'),
        description: t('添加失败'),
      });
      return null;
    }
  }
  function changeQueryType() {
    if (props.editData.id) {
      formState.configJson = '';
    }
  }
  defineExpose({
    handleSubmit,
  });
</script>

<style lang="less" scoped>
  .import-title {
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0 20px;
  }

  :deep(.ant-cascader) {
    width: 100%;
  }
</style>
