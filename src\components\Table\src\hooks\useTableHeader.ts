import { ComputedRef, Slots } from 'vue';
import type { BasicTableProps, InnerHandlers, TableActionType } from '../types/table';
import { unref, computed, h, Ref } from 'vue';
import TableHeader from '../components/TableHeader.vue';
import { isString } from '/@/utils/is';
import { getSlot } from '/@/utils/helper/tsxHelper';

export function useTableHeader(
  propsRef: ComputedRef<BasicTableProps>,
  slots: Slots,
  handlers: InnerHandlers,
  tabelWidth: Ref<Number>,
  calcToolBarWidth: Ref<Number>,
  getFormProps: any,
  getBindValues: Recordable,
  tableAction: TableActionType,
  handleSearchInfoChange: Function,
  redoHeight: Function,
  getFormSlotKeys: ComputedRef<string[]>,
  replaceFormSlotKey: Function,
  registerForm: Function,
) {
  const getHeaderProps = computed((): Recordable => {
    const {
      title,
      showTableSetting,
      titleHelpMessage,
      tableSetting,
      isAdvancedQuery,
      querySelectOption,
      objectId,
    } = unref(propsRef);
    const hideTitle = !slots.tableTitle && !title && !slots.toolbar && !showTableSetting;
    if (hideTitle && !isString(title)) {
      return {};
    }

    const formSlots = {};
    Object.keys(slots).forEach((key) => {
      if (key.startsWith('form-')) {
        formSlots[key] = getSlot(slots, key);
      }
    });

    return {
      title: hideTitle
        ? null
        : () =>
            h(
              TableHeader,
              {
                title,
                titleHelpMessage,
                showTableSetting,
                tableSetting,
                onColumnsChange: handlers.onColumnsChange,
                toolBarWidth: calcToolBarWidth.value,
                tableWidth: tabelWidth.value,
                getFormProps: getFormProps.value,
                getBindValues: getBindValues.value,
                tableAction,
                handleSearchInfoChange,
                redoHeight,
                slots,
                getFormSlotKeys,
                replaceFormSlotKey,
                registerForm,
                isAdvancedQuery,
                querySelectOption,
                objectId,
              } as Recordable,
              {
                ...(slots.toolbar
                  ? {
                      toolbar: () => getSlot(slots, 'toolbar'),
                    }
                  : {}),
                ...(slots.tableTitle
                  ? {
                      tableTitle: () => getSlot(slots, 'tableTitle'),
                    }
                  : {}),
                ...(slots.headerTop
                  ? {
                      headerTop: () => getSlot(slots, 'headerTop'),
                    }
                  : {}),
                ...(slots.headerContent
                  ? {
                      headerContent: () => getSlot(slots, 'headerContent'),
                    }
                  : {}),
                ...formSlots,
              },
            ),
    };
  });

  return { getHeaderProps };
}
