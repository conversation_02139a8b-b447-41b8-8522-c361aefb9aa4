<template>
  <a-time-picker
    :size="size"
    v-model:value="modelValue"
    :placeholder="placeholder"
    :format="format"
    :valueFormat="format"
    :allowClear="allowClear"
    :disabled="disabled"
    @change="handleChange"
  />
</template>
<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { ref, watch } from 'vue';

  const props = defineProps({
    value: [dayjs, String, Object],
    size: String,
    placeholder: String,
    format: String,
    allowClear: Boolean,
    disabled: Boolean,
  });
  const modelValue = ref<string>();
  const emit = defineEmits(['update:value', 'change']);
  watch(
    () => props.value,
    (val) => {
      if (val && typeof val !== 'string') {
        modelValue.value = val?.format(props.format);
      } else {
        modelValue.value = val || '';
      }
    },
    {
      immediate: true,
    },
  );
  const handleChange = (time) => {
    emit('update:value', time);
    emit('change');
  };
</script>
