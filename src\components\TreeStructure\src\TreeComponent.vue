<template>
  <div class="wrap-box" v-if="treeState.treeData.length > 0" :style="treeState.getBoxStyle">
    <template v-if="props.treeConfig.isMultiple">
      <a-directory-tree
        v-if="!treeState.loading"
        v-model:checkedKeys="checkedKeys"
        :selectable="false"
        :checkable="true"
        :tree-data="treeState.treeData"
        default-expand-all
        :show-icon="false"
        :height="treeState.height"
        @check="checkItem"
        :disabled="disabled"
      >
        <template #title="item"> &nbsp;&nbsp;{{ item.label }} </template>
      </a-directory-tree>
    </template>
    <template v-else>
      <a-directory-tree
        v-if="!treeState.loading"
        v-model:selectedKeys="selectedKeys"
        :tree-data="treeState.treeData"
        default-expand-all
        :show-icon="false"
        :height="treeState.height"
        :disabled="disabled"
      >
        <template #title="item"> &nbsp;&nbsp;{{ item.label }} </template>
      </a-directory-tree>
    </template>
  </div>
</template>
<script lang="ts" setup>
  import { onMounted, watch, ref, reactive } from 'vue';
  import { getInfo } from '/@/api/system/generator/treeStructure';
  import { TreeStructureType } from '/@/enums/treeStructure';
  import { apiConfigFunc } from '/@/utils/event/design';
  import { cloneDeep } from 'lodash-es';
  const props = defineProps({
    value: String,
    defaultValue: String,
    disabled: Boolean,
    codeType: String,
    treeConfig: {
      type: Object,
    },
  });
  const emit = defineEmits(['update:value', 'change']);
  const checkedKeys = ref<string[] | number[]>([]);
  const selectedKeys = ref<string[] | number[]>([]);
  const treeState = reactive({
    treeData: [
      {
        title: '0-0',
        key: '0-0',
        value: '0-0',
        label: '0-0',
      },
    ] as TreeProps['treeData'],
    height: 600,
    treeDataMap: new Map(),
    getBoxStyle: '',
    loading: true,
  });
  watch(
    () => props.treeConfig.id,
    async (_val) => {
      await getTreeData();
    },
  );
  watch(
    () => props.treeConfig.height,
    (val) => {
      initHeight(val);
    },
  );
  watch(
    () => props.value,
    (_val) => {
      initData();
    },
  );
  watch(
    () => props.treeConfig.isMultiple,
    async (_val) => {
      resetData();
    },
  );
  watch(checkedKeys, () => {
    changeSelectIds(checkedKeys.value);
  });
  watch(selectedKeys, () => {
    changeSelectIds(selectedKeys.value);
  });
  onMounted(async () => {
    initHeight(props.treeConfig.height);
    await getTreeData();
    initData();
  });
  function resetData() {
    let val = '';
    if (props.treeConfig.isMultiple) {
      checkedKeys.value = [];
      emit('update:value', val);
      emit('change', val);
    } else {
      selectedKeys.value = '';
      emit('update:value', val);
      emit('change', val);
    }
  }
  function initHeight(height) {
    treeState.height = Number(height);
    treeState.getBoxStyle = 'height: ' + (treeState.height + 20) + 'px;';
  }
  function initData() {
    if (props.value) {
      if (props.treeConfig.isMultiple) {
        let val = props.value.split(',');
        checkedKeys.value = val;
      } else {
        selectedKeys.value = props.value;
      }
    } else if (props.defaultValue) {
      if (props.treeConfig.isMultiple) {
        let val = props.defaultValue.split(',');
        checkedKeys.value = val;
      } else {
        selectedKeys.value = props.defaultValue;
      }
    }
  }
  async function getTreeData() {
    if (!props.treeConfig?.id) {
      return;
    }
    if (props.treeConfig.isMultiple) {
      checkedKeys.value = [];
    } else {
      selectedKeys.value = '';
    }
    let formState = await getInfo(props.treeConfig?.id);
    let config = JSON.parse(formState.config);
    console.log('config: ', config);
    if (formState.type == TreeStructureType.STATIC) {
      // treeState.treeData = config.staticData;
      let data: Array<ApiItem> = [];
      config.staticData.forEach((element, index) => {
        let res = getItem(element, index, element.value);
        data.push(res);
      });
      treeState.treeData = data;
    } else {
      let data: Array<ApiItem> = [];
      let apiData = await apiConfigFunc(config.apiData.apiConfig, false, {});
      let labelKey = config.apiData.apiColumns[0].dataIndex;
      let valueKey = config.apiData.apiColumns[1].dataIndex;
      apiData.data.forEach((element) => {
        let res = getApiItem(element, labelKey, valueKey, element[valueKey]);
        data.push(res);
      });
      treeState.treeData = data;
    }
    setTreeDataMap(cloneDeep(treeState.treeData));
    treeState.loading = false;
  }
  function getApiItem(item, labelKey, valueKey, value): ApiItem {
    let childrenArr: Array<ApiItem> = [];
    if (item.children.length > 0) {
      item.children.forEach((element) => {
        let value2 = value + '---' + element[valueKey];
        childrenArr.push(getApiItem(element, labelKey, valueKey, value2));
      });
    }
    // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
    let { children, ...otherParams } = item;
    return {
      ...otherParams,
      label: item[labelKey],
      key: value,
      children: childrenArr,
    };
  }
  function getItem(item, index, rowKey): ApiItem {
    let childrenArr: Array<ApiItem> = [];
    if (item.children.length > 0) {
      item.children.forEach((element, index2) => {
        let key = index + '---' + index2;
        let rowKey2 = rowKey + '---' + element.value;
        childrenArr.push(getItem(element, key, rowKey2));
      });
    }
    // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
    let { children, ...otherParams } = item;
    return {
      ...otherParams,
      label: item.label,
      index: index,
      key: rowKey,
      children: childrenArr,
    };
  }
  function setTreeDataMap(treeData) {
    if (treeData) {
      treeData.forEach((element) => {
        setTreeMapItem(element);
      });
    }
  }
  function setTreeMapItem(item) {
    if (item.children.length > 0) {
      item.children.forEach((element) => {
        setTreeMapItem(element);
      });
    }
    // eslint-disable-next-line no-unused-vars, @typescript-eslint/no-unused-vars
    let { children, ...otherParams } = item;
    treeState.treeDataMap.set(item.key, otherParams);
  }
  function changeSelectIds(ids) {
    console.log('ids: ', ids);
    let val = '';
    if (ids.length > 0) {
      if (props.treeConfig.isMultiple) {
        val = ids.join(',');
      } else {
        val = ids;
      }
    }
    emit('update:value', val);
    emit('change', val);
  }
  function checkItem(item) {
    checkedKeys.value = item;
  }
</script>
<style lang="less" scoped>
  .wrap-box {
    border: 1px solid #d9d9d9;
    padding: 10px;
  }
</style>
