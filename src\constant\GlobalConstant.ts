/**
 * 默认数据库Id
 */
export const DEFAULT_DB_ID = 'master';

export const START_EVENT_NAME = 'bpmn:StartEvent'; //开始节点
export const END_EVENT_NAME = 'bpmn:EndEvent'; //结束节点
export const INCLUSIVE_GATEWARY_NAME = 'bpmn:InclusiveGateway'; //相容网关
export const PARALLEL_GATEWARY_NAME = 'bpmn:ParallelGateway'; //并行网关
export const EXCLUSIVE_GATEWARY_NAME = 'bpmn:ExclusiveGateway'; //互斥网关
export const USER_TASK_NAME = 'bpmn:UserTask'; //用户任务
export const SCRIPT_TASK_NAME = 'bpmn:ScriptTask'; //脚本任务
export const SUB_PROCESS_NAME = 'bpmn:SubProcess'; //子流程
export const PROCESS_EVENT_NAME = 'bpmn:Process'; //流程
export const SEQUENCEFLOW_NAME = 'bpmn:SequenceFlow'; //流程线

export const WORKFLOW_KEY = {
  NODE_ADD_EVENT_KEY: 'shape.added', //新增节点事件名称
  NODE_REMOVE_EVENT_KEY: 'shape.removed', //移除节点事件名称
  NODE_CLICK_EVENT_KEY: 'element.click', //点击事件名称
  REGISTER_KEY: 'elementRegistry', //节点注册
  MODELING_KEY: 'modeling', //获取modling
  CAVAS_KEY: 'canvas', //画布操作
};
