#app {
  width: 100%;
  height: 100%;
  /* 移动端滚动优化 */
  -webkit-overflow-scrolling: touch;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
}

// =================================
// ==============scrollbar==========
// =================================

::-webkit-scrollbar {
  width: 7px;
  height: 8px;

  /* 移动端隐藏滚动条 */
  @media (max-width: 768px) {
    width: 0px;
    height: 0px;
  }
}

// ::-webkit-scrollbar-track {
//    background: rgb(0 0 0 / 5%);
// }

// ::-webkit-scrollbar-track:hover {
//   background-color: rgb(0 0 0 / 5%);
// }

::-webkit-scrollbar-thumb {
  // background: rgba(0, 0, 0, 0.6);
  background-color: rgb(144 147 153 / 30%);
  // background-color: rgba(144, 147, 153, 0.3);
  border-radius: 4px;
  // box-shadow: inset 0 0 6px rgb(0 0 0 / 20%);
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgb(144 147 153 / 50%);
}

/* 移动端滚动容器优化 */
.mobile-scroll-container {
  -webkit-overflow-scrolling: touch;
  overflow-scrolling: touch;
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: scroll-position;
  /* 防止iOS橡皮筋效果 */
  overscroll-behavior: contain;
  -webkit-overscroll-behavior: contain;
}

// =================================
// ==============nprogress==========
// =================================
#nprogress {
  pointer-events: none;

  .bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999;
    width: 100%;
    height: 2px;
    background-color: @primary-color;
    opacity: 0.75;
  }
}