import * as XLSX from 'xlsx';

// 合作产品信息模板
export const createCooperationProductTemplate = () => {
  const headers = ['产品名称', '规格', '单位', '协议价（元/盒）', '建议零售价（元/盒）'];
  const sampleData = [
    ['康缘桂枝茯苓胶囊', '0.31g*36粒', '盒', '0.00', '0.00']
  ];

  const ws = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, '合作产品信息');

  return wb;
};

// 年度任务量分解模板
export const createAnnualTaskTemplate = () => {
  const headers = [
    '产品名称',
    '规格',
    '全年任务量',
    '第一季度任务量',
    '第二季度任务量',
    '第三季度任务量',
    '第四季度任务量',
    '合作费用标准（元/盒）',
  ];
  const sampleData = [
    ['康缘桂枝茯苓胶囊', '0.31g*36粒', '/', '/', '/', '/', '/', '/']
  ];

  const ws = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, '年度任务量分解');

  return wb;
};

// 店员推荐体系产品模板
export const createEmployeeRecommendationTemplate = () => {
  const headers = [
    '产品名称',
    '规格',
    '连锁店员单品激励政策',
    '连锁货架推荐符号或标志'
  ];
  const sampleData = [
    ['康缘桂枝茯苓胶囊', '0.31g*36粒', '/', '/'],
  ];

  const ws = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, '店员推荐体系产品');

  return wb;
};
// 商业配送协议
export const createDeliveryProductTemplate = () => {
  const headers = [
    '连锁名称',
    '产品名称',
    '规格',
    '连锁协议价（元）',
    '商业开票价（元）',
    '差价（含配送费）（元）',
    '执行期间（支持 "2025-01-01--2025-12-31" 格式）',
  ];
  const sampleData = [
    ['xxx公司', '康缘桂枝茯苓胶囊', '0.31g*36粒', '/', '/', '/', '2025-01-01--2025-12-31'],
  ];

  const ws = XLSX.utils.aoa_to_sheet([headers, ...sampleData]);
  const wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, '商业配送协议-合作产品信息');

  return wb;
};
// 下载Excel模板
export const downloadTemplate = (templateType: 'product' | 'task' | 'employee' | 'delivery') => {
  let wb: XLSX.WorkBook;
  let filename: string;

  switch (templateType) {
    case 'product':
      wb = createCooperationProductTemplate();
      filename = '连锁合作协议-合作产品信息导入模板.xlsx';
      break;
    case 'task':
      wb = createAnnualTaskTemplate();
      filename = '连锁合作协议-年度任务量分解导入模板.xlsx';
      break;
    case 'employee':
      wb = createEmployeeRecommendationTemplate();
      filename = '连锁合作协议-店员推荐体系产品导入模板.xlsx';
      break;
    case 'delivery':
      wb = createDeliveryProductTemplate();
      filename = '商业配送协议-合作产品信息导入模板.xlsx';
      break;
    default:
      throw new Error('未知的模板类型');
  }

  XLSX.writeFile(wb, filename);
};
