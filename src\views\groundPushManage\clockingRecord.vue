<template>
  <div id="clockingRecord">
    <div class="targetMange_Box">
      <div class="type_top">
        <div class="btn_group">
          <div
            :class="recordType == 1 ? 'is_active ant-btn-primary' : ''"
            @click="changeRecordType(1)"
            >拜访打卡
          </div>
          <div
            :class="recordType == 2 ? 'is_active ant-btn-primary' : ''"
            @click="changeRecordType(2)"
            >协访打卡
          </div>
        </div>
      </div>
      <div class="filterForm_box">
        <a-select
          v-if="recordType == 1"
          v-model:value="searchForm.category"
          style="width: 240px"
          placeholder="拜访类型"
          :options="typeOptions"
          :field-names="{ label: 'name', value: 'value' }"
          allowClear
          @change="getList()"
        />
        <a-input
          v-if="recordType == 1"
          style="width: 240px"
          v-model:value.lazy="searchForm.salesmanNameOrEdpCode"
          placeholder="员工姓名/EDP工号"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-input
          v-if="recordType == 2"
          style="width: 240px"
          v-model:value.lazy="searchForm.coachedNameOrEdpCode"
          placeholder="协访人姓名/EDP工号"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-input
          v-if="recordType == 2"
          style="width: 240px"
          v-model:value.lazy="searchForm.salesmanNameOrEdpCode"
          placeholder="协访对象姓名/EDP工号"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-input
          style="width: 240px"
          v-model:value.lazy="searchForm.purMerchantName"
          placeholder="客户名称"
          allowClear
          @change="getList()"
          @pressEnter="getList()"
        />
        <a-range-picker
          v-model:value="searchForm.time"
          :placeholder="['签到时间', '结束时间']"
          @change="getList()"
        />
        <a-button type="primary" @click="getList()">搜索</a-button>
        <a-button @click="reSet()">重置</a-button>
        <a-button class="right_btn" type="primary" @click="onSignSet">签到设置</a-button>
        <a-button class="right_btn" type="primary" @click="onShopSet">到店事项设置</a-button>
      </div>
      <div class="p_box">
        <a-checkbox v-model:checked="searchForm.isException" @change="getList()"
          >异常数据
        </a-checkbox>
      </div>
      <div class="table_box">
        <c-table
          :tableColumns="tableColumns"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
        >
          <template #sign="{ record, column }">
            <span :class="record[`${column.key}ExceptionStatus`] === 1 ? '' : 'tipRed'"
              >{{ record[`${column.key}ExceptionReasonName`]
              }}<span
                v-if="record[`${column.key}ExceptionReason`] == 1 && record[`${column.key}Offset`]"
                >（{{ record[`${column.key}Offset`] }}m）</span
              ></span
            >
          </template>
          <template #betWeenMinus="{ record }">
            <span>{{ record.betWeenMinus }}分钟</span>
          </template>
          <template #action="{ record }">
            <a-button type="link" v-if="recordType == 1" @click.stop="onLogs(record)"
              >查看日志
            </a-button>
            <a-button type="link" v-if="recordType == 2" @click.stop="onEstimate(record)"
              >协访评价
            </a-button>
          </template>
        </c-table>
      </div>
    </div>
  </div>
  <a-modal
    :width="600"
    v-model:visible="openSignVisible"
    title="签到设置"
    :confirm-loading="modalLoading"
    :maskClosable="false"
    destroyOnClose
    centered
    @ok="handleModalOk"
    @cancel="() => (openSignVisible = false)"
  >
    <div class="modal_box">
      <a-form
        class="form_box"
        ref="formRef"
        :model="modalInfo"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 19 }"
        autocomplete="off"
        labelAlign="right"
        :colon="true"
      >
        <a-form-item label="打卡范围" name="outerScope" required>
          <a-input-number
            style="width: 220px"
            v-model:value.lazy="modalInfo.outerScope"
            show-count
            placeholder="请输入"
            allowClear
            :min="0"
          />
          米
        </a-form-item>
        <a-form-item label="超范围打卡" name="superableScope" required>
          <a-radio-group v-model:value="modalInfo.superableScope" name="isShow">
            <a-radio :style="radioStyle" :value="2">不可打卡</a-radio>
            <a-radio :style="radioStyle" :value="1">可打卡，但需要补充说明</a-radio>
          </a-radio-group>
        </a-form-item>
        <a-form-item label="有效拜访时间" name="durationVisit">
          <a-input-number
            style="width: 220px"
            v-model:value.lazy="modalInfo.durationVisit"
            show-count
            placeholder="请输入"
            allowClear
            :min="0"
            :max="1440"
          />
          分钟
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
  <a-modal
    :width="600"
    v-model:visible="openShopVisible"
    title="到店事项设置"
    :confirm-loading="modalLoading"
    :maskClosable="false"
    destroyOnClose
    centered
    @ok="save"
    @cancel="() => (openShopVisible = false)"
  >
    <div class="modal_box">
      <a-tabs @change="changeShop">
        <a-tab-pane key="0" tab="门店">
          <a-table
            bordered
            :data-source="dataSourceShop"
            :columns="columnsShop"
            :pagination="false"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'signCount'">
                <div class="editable-cell-input-wrapper" v-if="record.key != 0">
                  <a-input-number
                    style="width: 100%"
                    v-model:value="dataSourceShop[record.key][column.dataIndex]"
                    :min="0"
                    :max="100000000"
                  />
                </div>
              </template>
              <template v-if="column.dataIndex === 'checked'">
                <div class="editable-cell-input-wrapper">
                  <a-switch
                    :disabled="record.key == 0"
                    v-model:checked="dataSourceShop[record.key][column.dataIndex]"
                    checked-children="是"
                    un-checked-children="否"
                  />
                </div>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="1" tab="分部">
          <a-table
            bordered
            :data-source="dataSourceShop"
            :columns="columnsShop"
            :pagination="false"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'signCount'">
                <div class="editable-cell-input-wrapper">
                  <a-input-number
                    style="width: 100%"
                    v-model:value="dataSourceShop[record.key][column.dataIndex]"
                    :min="0"
                    :max="100000000"
                  />
                </div>
              </template>
              <template v-if="column.dataIndex === 'checked'">
                <div class="editable-cell-input-wrapper">
                  <a-switch
                    :disabled="true"
                    :checked="dataSourceShop[record.key]['mattersType'] === 1"
                    checked-children="是"
                    un-checked-children="否"
                  />
                </div>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
        <a-tab-pane key="2" tab="总部">
          <a-table
            bordered
            :data-source="dataSourceShop"
            :columns="columnsShop"
            :pagination="false"
          >
            <template #bodyCell="{ column, text, record }">
              <template v-if="column.dataIndex === 'signCount'">
                <div class="editable-cell-input-wrapper" v-if="record.key != 0">
                  <a-input-number
                    style="width: 100%"
                    v-model:value="dataSourceShop[record.key][column.dataIndex]"
                    :min="0"
                    :max="100000000"
                  />
                </div>
              </template>
              <template v-if="column.dataIndex === 'checked'">
                <div class="editable-cell-input-wrapper">
                  <a-switch
                    :disabled="record.key == 0"
                    v-model:checked="dataSourceShop[record.key][column.dataIndex]"
                    checked-children="是"
                    un-checked-children="否"
                  />
                </div>
              </template>
            </template>
          </a-table>
        </a-tab-pane>
      </a-tabs>
    </div>
  </a-modal>
  <a-modal
    :width="650"
    v-model:visible="openEstimateVisible"
    title="协访评价"
    :footer="null"
    :maskClosable="false"
    destroyOnClose
    centered
    @cancel="() => (openEstimateVisible = false)"
  >
    <div class="modal_box">
      <a-table
        :columns="columnsEstimate"
        :data-source="dataEstimate"
        :loading="estimateloading"
        :pagination="false"
        bordered
      />
    </div>
  </a-modal>
  <a-modal
    :width="1000"
    v-model:visible="openLogsVisible"
    :destroyOnClose="true"
    title="拜访日志"
    :footer="null"
    :maskClosable="false"
    centered
    @cancel="closeModel()"
  >
    <div class="modal_box">
      <p class="p1"
        ><span>拜访类型：{{ logsInfo.categoryName ?? '' }}</span
        ><span>拜访人：{{ logsInfo.personName ?? '' }}</span></p
      >
      <p class="p2">{{ logsInfo.purMerchantName }}</p>
      <p class="p3">{{ logsInfo.destination }}</p>
      <a-table
        size="small"
        bordered
        :data-source="dataSourceShopView"
        :columns="columnsShopView"
        :pagination="false"
        :expanded-row-keys="expandedRowKeys"
        @expand="handleExpand"
      >
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'status'">
            <div class="editable-cell-input-wrapper">
              <CheckCircleFilled v-if="text === 1 || text === 5" style="color: green" />
              <CloseCircleFilled v-else style="color: red" />
            </div>
          </template>
        </template>
        <template #expandedRowRender="{ record, index }">
          <div style="margin: 0" v-if="index == 0">
            <p class="p3"
              >在店时长：<span :style="{ color: getThemeColor }"
                >{{ logsInfo.betWeenMinus ?? '0' }}分钟</span
              ></p
            >
            <p class="p4"
              ><span style="display: inline-block; width: 130px">{{ logsInfo.signDate }}</span
              ><span>签到</span
              ><span :class="logsInfo.signExceptionStatus == 1 ? 'is_normal' : 'is_abnormal'"
                >{{ logsInfo.signExceptionReasonName
                }}<span v-if="logsInfo.signExceptionReason == 1 && logsInfo.signOffset"
                  >{{ logsInfo.signOffset }}m</span
                ></span
              ></p
            >
            <div
              class="p3"
              style="font-size: 12px; color: #c10000; margin-left: 145px"
              v-if="logsInfo.superableScopeRemarks"
              >超范围备注：{{ logsInfo.superableScopeRemarks }}</div
            >
            <div class="line"></div>
            <p class="p4"
              ><span style="display: inline-block; width: 130px">{{
                logsInfo.signOutDate || '暂无签退时间'
              }}</span
              ><span>签退</span
              ><span :class="logsInfo.signOutExceptionStatus == 1 ? 'is_normal' : 'is_abnormal'"
                >{{ logsInfo.signOutExceptionReasonName
                }}<span v-if="logsInfo.signOutExceptionReason == 1 && logsInfo.signOutOffset"
                  >: {{ logsInfo.signOffset }}m</span
                ></span
              ></p
            >
            <div
              class="p3"
              style="font-size: 12px; color: #c10000; margin-left: 145px"
              v-if="logsInfo.descrOutOfScope"
              >超范围备注：{{ logsInfo.descrOutOfScope }}</div
            >
            <p class="p5">
              <!-- v-if="logsInfo.img" -->
              <img v-for="(item, index) in logsInfo.imageUrls" :key="index" :src="item" alt="" />
            </p>
          </div>
          <div v-if="record.mattersType === 2">
            <a-table
              size="small"
              bordered
              :data-source="dataProductFinance"
              :columns="columnProductFinance"
              :pagination="false"
              row-key="inventoryNo"
            >
              <template #expandedRowRender="{ record, index }">
                <a-image
                  v-for="(item, index) in record.fileUrls"
                  :key="index"
                  :src="item"
                  :width="100"
                />
              </template>
            </a-table>
          </div>
          <div v-if="record.mattersType === 3">
            <a-table
              size="small"
              bordered
              :data-source="dataColumnGoodCollect"
              :columns="columnGoodCollect"
              :pagination="false"
            >
            </a-table>
          </div>
          <div v-if="record.mattersType === 4">
            <h4
              >促销产品：<span style="font-weight: normal">{{
                ticketData.productGroupName
              }}</span></h4
            >
            <template v-for="(item, index) in ticketData.ticketList">
              <h5
                >小票{{ index + 1 }}：
                <span style="font-weight: normal; margin-left: 20px"
                  >门店：{{ item.shopName }}</span
                >
                <span style="font-weight: normal; margin-left: 20px"
                  >流水号：{{ item.waterNo }}</span
                >
                <span style="font-weight: normal; margin-left: 20px"
                  >时间：{{ item.ticketTime }}</span
                >
              </h5>
              <div style="margin: 20px 0">
                <a-table
                  size="small"
                  bordered
                  :data-source="item.storePromotionTicketDetailVos"
                  :columns="columnTicket"
                  :pagination="false"
                >
                </a-table>
              </div>
            </template>
            <h4
              >销售额（合计）：<span style="font-weight: normal">{{
                ticketData?.totalMoney
              }}</span></h4
            >
            <h4
              >促销照片：<br />
              <a-image
                v-for="(item, index) in ticketData.filePathsList"
                :key="index"
                :src="item"
                :width="120"
              />
            </h4>
            <h4
              >活动总结：<span style="font-weight: normal">{{ ticketData?.comment }}</span></h4
            >
            <p class="p5" v-if="inShopSell.imgList.length > 0">
              <a-image
                v-for="(item, index) in inShopSell.imgList"
                :key="index"
                :src="item"
                :width="120"
              />
            </p>
          </div>
          <div v-if="record.mattersType === 5">
            <a-table
              size="small"
              bordered
              :data-source="dataSickNote"
              :columns="columnSickNote"
              :pagination="false"
              rowKey="id"
            >
              <template #expandedRowRender="{ record, index }">
                <a-table
                  style="width: 80%; margin-left: 40px"
                  size="small"
                  bordered
                  :data-source="record.otcPatientRecordMedicineDtos"
                  :columns="columnsMedicineName"
                  :pagination="false"
                  :rowKey="index"
                ></a-table>
              </template>
            </a-table>
          </div>
          <div v-if="record.mattersType === 6">
            <a-table
              size="small"
              bordered
              :data-source="dataUserNote"
              :columns="columnUserNote"
              :pagination="false"
            >
              <template #expandedRowRender="{ record, index }">
                <a-table
                  style="width: 80%; margin-left: 40px"
                  size="small"
                  bordered
                  :data-source="record.workTime"
                  :columns="columnsWeekList"
                  :pagination="false"
                  :rowKey="index"
                ></a-table>
              </template>
            </a-table>
          </div>
          <div v-if="record.mattersType === 7">
            <h4
              >培训产品：<span style="font-weight: normal">{{
                trainData.productGroupName
              }}</span></h4
            >
            <h4>培训视频：</h4>
            <p class="p5" v-if="inShopSell.imgList.length > 0">
              <video
                :src="inShopSell.imgList[0]"
                style="width: 150px; height: 100%"
                controls
                ref="videoRef"
                @loadedmetadata="onLoadedMetadata"
              ></video>
            </p>
            <h4 v-if="duration > 0">视频时长: {{ duration }} 秒</h4>
            <h4
              >培训总结：<span style="font-weight: normal">{{ inShopSell.comment }}</span></h4
            >
          </div>
          <div v-if="record.mattersType === 8">
            <div class="p5 boxCheck" v-if="inShopSell.detailList.length > 0">
              <template v-for="(item, index) in inShopSell.detailList" :key="index">
                <p>图片{{ index + 1 }}</p>
                <a-image :src="item?.filePath" :width="120" :height="150"/>
                <a-table
                  :pagination="false"
                  :data-source="item?.list"
                  style="width: 90%"
                  size="small"
                  bordered
                  :columns="columnsBoxCheck"
                  :rowKey="index"
                >
                  <template #action="{ record }">
                    <a v-if="record?.stockFilePath" @click="() => previewImage(record?.stockFilePath)">查看凭证</a>
                    <span v-else>无</span>
                  </template>
                </a-table>
              </template>
            </div>
          </div>
        </template>
      </a-table>
    </div>
  </a-modal>

  <!-- 图片预览组件 -->
  <a-image
    :style="{ display: 'none' }"
    :preview="{
      visible: previewVisible,
      onVisibleChange: setPreviewVisible,
      src: previewImageUrl
    }"
    :src="previewImageUrl"
  />
</template>

<script lang="ts" setup>
  import cTable from '/@/views/components/Table/index.vue';
  import { onMounted, reactive, ref } from 'vue';
  import { recordTableColumns, reasonList } from './common';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons-vue';
  const { notification } = useMessage();
  import { useRootSetting } from '/@/hooks/setting/useRootSetting';

  const { getThemeColor } = useRootSetting();
  import {
    getAdminPage,
    updateSet,
    getSet,
    updateVisitMattersList,
    updateVisitMatters,
  } from '/@/api/groundPushManage/clockingRecord';
  import {
    getDicDetailList,
    getPersionMattersList,
    getOtcStorePromotion,
    getOtcStoreTraining,
    getVisitLogSetVo,
    getOtcStoreDisplayCheck,
  } from '/@/api/system/dic';
  import dayjs from 'dayjs';

  const searchForm = reactive({
    salesmanNameOrEdpCode: '',
    purMerchantName: '',
    coachedNameOrEdpCode: '',
    category: undefined,
    isException: false,
    time: null,
  });
  const reSet = () => {
    searchForm.salesmanNameOrEdpCode = '';
    searchForm.purMerchantName = '';
    searchForm.coachedNameOrEdpCode = '';
    searchForm.category = undefined;
    searchForm.isException = false;
    searchForm.time = null;
    getList();
  };
  const tableColumns = ref<any[]>([]);
  const tableData = reactive({
    data: [],
  });
  const loading = ref(false);
  const pagination = reactive({
    currentPage: 1,
    totalItems: 500,
    pageSize: 10,
  });
  const videoRef = ref(null);
  const duration = ref(0);
  const visible = ref<boolean>(false);

  // 图片预览相关
  const previewVisible = ref<boolean>(false);
  const previewImageUrl = ref<string>('');

  const previewImage = (imageUrl: string) => {
    previewImageUrl.value = imageUrl;
    previewVisible.value = true;
  };

  const setPreviewVisible = (value: boolean) => {
    previewVisible.value = value;
    if (!value) {
      previewImageUrl.value = '';
    }
  };

  // 新增：当视频元数据加载完成后获取总时长
  const onLoadedMetadata = () => {
    if (videoRef.value) {
      duration.value = videoRef.value.duration ? parseInt(videoRef.value.duration) : 0;
    }
  };
  const changeShop = (val) => {
    let key
    if (val === '2') {
      key = '0'
    } else if (val === '0'){
      key = '2'
    } else {
      key = val;
    }
    VisitMattersList(key);
  };
  const getList = async (flag?: number) => {
    if (!flag) {
      pagination.currentPage = 1;
      pagination.pageSize = 10;
    }
    loading.value = true;
    tableData.data = [];
    try {
      let temp = {
        ...searchForm,
        coachedType: recordType.value === 1 ? 0 : 1,
        isException: searchForm.isException ? 1 : 0,
        startTime: searchForm.time?.[0]
          ? dayjs(searchForm.time?.[0]).format('YYYY-MM-DD 00:00:00')
          : '',
        endTime: searchForm.time?.[1]
          ? dayjs(searchForm.time?.[1]).format('YYYY-MM-DD 23:59:59')
          : '',
        limit: pagination.currentPage,
        size: pagination.pageSize,
      };
      let res = await getAdminPage(temp);
      pagination.totalItems = res.total ?? 0;
      tableData.data =
        res.list.map((item) => {
          let sign = reasonList.find((it) => it.value == item.signExceptionReason);
          let signOut = reasonList.find((it) => it.value == item.signOutExceptionReason);
          let category = typeOptions.value.find((ite) => ite.value == item.category);
          return {
            ...item,
            signExceptionReasonName: sign?.name ?? '',
            signOutExceptionReasonName: signOut?.name ?? '',
            categoryName: category?.name ?? '',
          };
        }) ?? [];
      loading.value = false;
    } catch (error) {
      console.log(error);
      loading.value = false;
    }
  };
  // 处理分页
  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    getList(1);
  };
  const recordType = ref(1); // 打卡类型
  const changeRecordType = (val: number) => {
    recordType.value = val;
    tableColumns.value = recordTableColumns[val];
    reSet();
  };
  onMounted(() => {
    tableColumns.value = recordTableColumns[recordType.value];
    getTypeOptions();
    getList();
  });

  // 签到设置
  const openSignVisible = ref(false);
  const openShopVisible = ref(false);
  type ModelInfo = {
    outerScope: number | null;
    superableScope: number;
  };
  const modalInfo = ref<ModelInfo>({
    outerScope: null,
    superableScope: 1,
  });
  const radioStyle = reactive({
    display: 'flex',
    height: '30px',
    lineHeight: '30px',
  });
  const onSignSet = async () => {
    try {
      let res = await getSet({ id: 1 });
      modalInfo.value = {
        ...res,
      };
      openSignVisible.value = true;
    } catch (error) {
      console.log(error);
    }
  };
  const modalLoading = ref<boolean>(false);
  const formRef = ref();
  const handleModalOk = () => {
    formRef.value
      .validate()
      .then(async () => {
        try {
          openSignVisible.value = true;
          let temp = {
            ...modalInfo.value,
            superableExplain: modalInfo.value.superableScope,
          };
          await updateSet(temp);
          notification.success({
            message: '提示',
            description: '操作成功',
          });
          openSignVisible.value = false;
        } catch (error) {
          openSignVisible.value = false;
        }
        openSignVisible.value = false;
      })
      .catch((error) => {
        console.log('error', error);
      });
  };

  // 协访评价
  const openEstimateVisible = ref<boolean>(false);
  const columnsEstimate = [
    {
      title: '维度',
      dataIndex: 'title',
      key: 'title',
      align: 'center',
      width: 200,
    },
    {
      title: '评分',
      dataIndex: 'value',
      key: 'value',
      align: 'center',
    },
  ];
  const dataEstimate = ref([
    {
      label: 'preVisitPlan',
      title: '访前计划',
      value: '',
    },
    {
      label: 'workAttitude',
      title: '工作态度',
      value: '',
    },
    {
      label: 'cusRelationshipManagement',
      title: '客情管理',
      value: '',
    },
    {
      label: 'statementInterests',
      title: '利益陈述',
      value: '',
    },
    {
      label: 'evaluate',
      title: '综合评价',
      value: '',
    },
    {
      label: 'suggestion',
      title: '协访建议',
      value: '',
    },
  ]);
  const estimateloading = ref(false);
  const onEstimate = (row: any) => {
    openEstimateVisible.value = true;
    estimateloading.value = true;
    try {
      dataEstimate.value.forEach((item) => {
        item.value = row[item.label] ?? '';
      });
      estimateloading.value = false;
    } catch (error) {
      console.log(error);
      estimateloading.value = false;
    }
  };
  const dataSourceShopView = ref<any[]>([]);
  const dataProductFinance = ref<any[]>([]);
  const dataColumnGoodCollect = ref<any[]>([]);
  const dataSickNote = ref<any[]>([]);
  const dataUserNote = ref<any[]>([]);
  const inShopSell = reactive<any>({
    detailList: [],
  });
  const trainData = ref<any>({});
  const ticketData = ref({
    productGroupName: '',
    ticketList: [],
    filePathsList: [],
    totalMoney: 0,
    comment: '',
  });
  // 查看日志
  const openLogsVisible = ref<boolean>(false);
  type LogsInfo = {
    [key: string]: any;
  };
  const logsInfo = ref<LogsInfo>({});
  const onLogs = (row: any) => {
    logsInfo.value = { ...row };
    getPersionMattersList({ serialNumber: row.serialNumber }).then((res) => {
      dataSourceShopView.value = res.map((e, index) => {
        return {
          ...e,
          key: index,
          checked: e.status == 1 ? true : false,
        };
      });
      if (row.category != 1) {
        dataSourceShopView.value = [dataSourceShopView.value[0]];
      }
      openLogsVisible.value = true;
    });
  };
  const closeModel = () => {
    openLogsVisible.value = false;
    expandedRowKeys.value = [];
    dataSourceShopView.value = [];
    dataProductFinance.value = [];
    dataColumnGoodCollect.value = [];
    dataSickNote.value = [];
    dataUserNote.value = [];
  };

  // 获取拜访类型
  const typeOptions = ref<any[]>([]);
  const getTypeOptions = async () => {
    let res = await getDicDetailList({ itemId: '1871819555403079682' });
    typeOptions.value = res;
  };
  const columnsShop = [
    {
      title: '事项名称',
      dataIndex: 'mattersName',
      width: '30%',
    },
    {
      title: '是否必做',
      dataIndex: 'checked',
    },
    {
      title: '等于签到次数',
      dataIndex: 'signCount',
    },
  ];
  const columnProductFinance = [
    {
      title: '产品名称',
      dataIndex: 'productFormatName',
      width: '30%',
    },
    {
      title: '本月购进(盒)',
      dataIndex: 'productBatchNo',
    },
    {
      title: '本月纯销(盒)',
      dataIndex: 'sellPrice',
    },
    {
      title: '门店库存(盒)',
      dataIndex: 'inventoryNum',
    },
  ];
  const columnGoodCollect = [
    {
      title: '竞品名称',
      dataIndex: 'productName',
    },
    {
      title: '竞品通用名',
      dataIndex: 'competitorCommonName',
    },
    {
      title: '竞品商用名',
      dataIndex: 'competitorGoodsName',
    },
    {
      title: '竞品SKU',
      dataIndex: 'competitorSku',
    },
    {
      title: '品牌',
      dataIndex: 'competitorBrand',
    },
    {
      title: '推荐级别',
      dataIndex: 'recommendLevel',
    },
    {
      title: '零售价(元)',
      dataIndex: 'competitorSellPrice',
    },
    {
      title: '供货价(元)',
      dataIndex: 'competitorSupplyPrice',
    },
    {
      title: '上月销量(盒)',
      dataIndex: 'lastMonthSales',
    },
    {
      title: '本月销量(盒)',
      dataIndex: 'currentMonthSales',
    },
    {
      title: '当月促销政策',
      dataIndex: 'promotionPolicy',
    },
  ];
  const columnTicket = [
    {
      title: '产品名称',
      dataIndex: 'name',
    },
    {
      title: '规格',
      dataIndex: 'spec',
    },
    {
      title: '数量',
      dataIndex: 'number',
    },
    {
      title: '销售额',
      dataIndex: 'totalPrice',
    },
  ];
  const columnSickNote = [
    {
      title: '患者姓名',
      dataIndex: 'patientName',
    },
    {
      title: '性别',
      dataIndex: 'genderName',
    },
    {
      title: '年龄',
      dataIndex: 'age',
    },
    {
      title: '联系方式',
      dataIndex: 'mobile',
    },
    {
      title: '地址',
      dataIndex: 'address',
    },
    {
      title: '购药时间',
      dataIndex: 'buyTime',
    },
  ];
  const columnUserNote = [
    {
      title: '员工姓名',
      dataIndex: 'employeeName',
    },
    {
      title: '职位',
      dataIndex: 'postName',
    },
    {
      title: '生日',
      dataIndex: 'birthDay',
    },
    {
      title: '联系方式',
      dataIndex: 'mobile',
    },
    {
      title: '家庭地址',
      dataIndex: 'address',
    },
    {
      title: '爱好',
      dataIndex: 'hobby',
    },
    {
      title: '工作状态',
      dataIndex: 'employing',
    },
    {
      title: '月成单金额',
      dataIndex: 'monthSalesMoney',
    },
  ];
  const columnsShopView = [
    {
      title: '事项名称',
      dataIndex: 'mattersName',
    },
    {
      title: '完成状态',
      dataIndex: 'status',
    },
  ];
  const columnsMedicineName = [
    {
      title: '药品名称',
      dataIndex: 'medicineName',
    },
    {
      title: '数量',
      dataIndex: 'medicineNum',
    },
    {
      title: '价格',
      dataIndex: 'medicinePrice',
    },
  ];
  const columnsWeekList = [
    {
      title: '时间',
      dataIndex: 'index',
    },
    {
      title: '上班时间',
      dataIndex: 'morning',
    },
    {
      title: '下班时间',
      dataIndex: 'after',
    },
  ];
  const columnsBoxCheck = [
    {
      title: '序号',
      dataIndex: 'index',
      customRender: ({ index }) => `${index + 1}`,
      align: 'center',
    },
    {
      title: '产品名称',
      dataIndex: 'productName',
      align: 'center',
    },
    {
      title: '规格',
      dataIndex: 'productSpec',
      align: 'center',
    },
    {
      title: '零售价',
      dataIndex: 'productPrice',
      align: 'center',
    },
    {
      title: '爆炸贴',
      dataIndex: 'productSticker',
      align: 'center',
      customRender: ({ record }) => {
        return record.productSticker == 1 ? '有' : '无';
      },
    },
    {
      title: '陈列面数量',
      dataIndex: 'productDisplayNum',
      align: 'center',
    },
    {
      title: '库存数量',
      dataIndex: 'productStock',
      align: 'center',
    },
    {
      title: '库存凭证',
      dataIndex: 'action',
      align: 'center',
      slots: { customRender: 'action' },
    },
  ];
  const dataSourceShop = ref<any[]>([]);
  const dataSourceBranch = ref<any[]>([]);
  const grandTotal = ref(0);

  const onShopSet = async () => {
    VisitMattersList('2');
  };
  const VisitMattersList = async (terminalType) => {
    try {
      let res = await updateVisitMattersList({ terminalType });
      dataSourceShop.value = res.map((e, index) => {
        return {
          ...e,
          key: index,
          checked: e.mustDo == 1 ? true : false,
        };
      });
      openShopVisible.value = true;
    } catch (error) {
      console.log(error);
    }
  };
  const save = () => {
    console.log(dataSourceShop, 666);
    try {
      const temp = dataSourceShop.value.map((e) => {
        return {
          ...e,
          mustDo: e.checked ? 1 : 2,
        };
      });
      updateVisitMatters(temp).then(() => {
        notification.success({
          message: '提示',
          description: '操作成功',
        });
        openShopVisible.value = false;
      });
    } catch (error) {
      openShopVisible.value = false;
    }
  };
  // 添加展开行的控制
  const expandedRowKeys = ref<any>([]);
  const handleExpand = (expanded, record) => {
    if (expanded && !record.children) {
      inShopSell.comment = '';
      inShopSell.imgList = [];
      expandedRowKeys.value = [record.key]; // 只保留当前展开的行
      // 签到拍照
      if (
        record.mattersType === 2 ||
        record.mattersType === 3 ||
        record.mattersType === 5 ||
        record.mattersType === 6
      ) {
        getVisitLogSetVo({
          serialNumber: record.serialNumber,
          mattersType: record.mattersType,
        }).then((res) => {
          if (record.mattersType === 2) {
            dataProductFinance.value = res.productInventoryCheckVoList;
          } else if (record.mattersType === 3) {
            dataColumnGoodCollect.value = res.productCompetitorListVo;
          } else if (record.mattersType === 5) {
            dataSickNote.value = res.otcPatientVoList.map((e) => {
              return {
                key: e.otcPatientRecordDto.id,
                ...e.otcPatientRecordDto,
                otcPatientRecordMedicineDtos: e.otcPatientRecordMedicineDtos,
              };
            });
            console.log(dataSickNote.value, 8888);
          } else if (record.mattersType === 6) {
            const weekList = ['每天', '周一', '周二', '周三', '周四', '周五', '周六', '周日'];
            dataUserNote.value = res.accountEmployeeList.map((e) => {
              return {
                key: e.id,
                ...e,
                workTime: Object.values(JSON.parse(e.workTime)).map((item, i) => {
                  return {
                    ...item,
                    index: weekList[i],
                  };
                }),
              };
            });
          }
          console.log(dataUserNote.value);
        });
      }
      // 驻点促销
      if (record.mattersType === 4) {
        getOtcStorePromotion({ serialNumber: record.serialNumber }).then((res) => {
          if (res) {
            ticketData.value = res;
            ticketData.value.productGroupName = !!res.productGroupName
              ? res.productGroupName
                  .split(',')
                  .map((item) => {
                    return item;
                  })
                  .join('、')
              : '';
            ticketData.value.ticketList =
              res.storePromotionDetailVos && res.storePromotionDetailVos.length > 0
                ? res.storePromotionDetailVos
                : [];
            ticketData.value.filePathsList = res.filePathsList;
            ticketData.value.totalMoney = res.totalMoney;
            ticketData.value.comment = res.comment;
          }
        });
      }
      // 贴柜培训
      if (record.mattersType === 7) {
        getOtcStoreTraining({ serialNumber: record.serialNumber }).then((res) => {
          inShopSell.comment = res.comment;
          inShopSell.imgList[0] = res?.preFilePath;
          trainData.value.productGroupName = res.productGroupName;
        });
      }
      // 陈列检查
      if (record.mattersType === 8) {
        inShopSell.detailList = []
        getOtcStoreDisplayCheck({ serialNumber: record.serialNumber }).then((res) => {
          // inShopSell.comment = '';
          // inShopSell.imgList = res.filePathsList;
          inShopSell.detailList = res?.detailList || [];
        });
      }
      // 调用接口获取展开数据
    } else {
      expandedRowKeys.value = expandedRowKeys.value.filter((key) => key !== record.key);
    }
  };
</script>

<style scoped lang="less">
  ::v-deep {
    .ant-table.ant-table-small .ant-table-tbody .ant-table-wrapper:only-child .ant-table {
      margin: 0px !important;
    }
  }
  #clockingRecord {
    width: 100%;
    height: 100%;
    padding: 8px;

    .targetMange_Box {
      width: 100%;
      height: 100%;
      background-color: #fff;
      display: flex;
      flex-direction: column;

      > div {
        width: 100%;
        padding: 16px;

        &:last-child {
          flex: 1;
          height: 0;
        }
      }

      .p_box {
        padding: 0 16px;
      }

      .type_top {
        width: 100%;
        padding: 16px 0 0 16px;

        .btn_group {
          display: inline-flex;

          > div {
            padding: 6px 40px;
            border: 1px solid #d9d9d9;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
            cursor: pointer;

            &:first-child {
              border-right: none;
              border-radius: 3px 0 0 3px;
            }

            &:last-child {
              border-left: none;
              border-radius: 0 3px 3px 0;
            }
          }

          .is_active {
            color: #fff;
          }
        }
      }
    }

    .filterForm_box {
      * + * {
        margin-left: 16px;
      }

      .right_btn {
        float: right;
      }
    }

    .table_box {
      .tipRed {
        color: #c10000;
      }
    }
  }

  .modal_box {
    padding: 16px;

    .form_box {
      margin-top: 10px;
    }

    :deep(.ant-form-item-explain-error) {
      display: none !important;
    }

    p {
      margin-bottom: 8px;
    }

    .p1 {
      font-size: 16px;
      line-height: 30px;
      margin-bottom: 16px;
      color: #000;

      span + span {
        margin-left: 120px;
      }
    }

    .p2 {
      font-size: 14px;
      color: #000;
      margin-bottom: 4px;
    }

    .p3 {
      color: rgba(0, 0, 0, 0.7);
      margin-bottom: 16px;
    }

    .p4 {
      color: rgba(0, 0, 0, 0.6);

      > span + span {
        margin-left: 16px;
      }

      .is_normal {
        color: rgba(0, 0, 0, 0.8);
      }

      .is_abnormal {
        color: #c10000;
      }
    }

    .p5 {
      padding-top: 8px;
      display: inline-flex;
      flex-wrap: wrap;
      gap: 12px;

      > img {
        width: 120px;
        height: 120px;
      }
      > video {
        width: 120px;
        height: 120px;
      }
    }

    .boxCheck {
      display: flex;
      flex-direction: column;
    }

    .line {
      margin-left: 25px;
      width: 2px;
      height: 25px;
      background-color: rgba(0, 0, 0, 0.2);
      margin-bottom: 8px;
    }
  }
</style>
