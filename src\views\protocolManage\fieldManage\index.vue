<template>
  <div class="fieldManage">
    <div class="page-container">
      <div class="explore">
        <a-input
          style="width: 240px"
          v-model:value="searchForm.name"
          placeholder="请输入字段名称"
        />
        <a-input
          style="width: 240px"
          v-model:value="searchForm.code"
          placeholder="请输入字段编号"
        />
        <a-select
          style="width: 240px"
          v-model:value="searchForm.required"
          placeholder="请选择"
          allow-clear
        >
          <a-select-option value="1">是</a-select-option>
          <a-select-option value="0">否</a-select-option>
        </a-select>
        <div class="button-style">
          <a-button type="primary" @click="getList()">搜索</a-button>
          <a-button @click="reSet()">重置</a-button>
          <a-button type="primary" @click="handleAdd()">新增</a-button>
        </div>
      </div>
      <div class="table_box">
        <c-table
          :tableColumns="tableColumns"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
        >
          <template #required="{ record }">
            <span>{{ record.required ? '是' : '否' }}</span>
          </template>
          <template #action="{ record }">
            <a-button-group>
              <a-button type="link" size="small" @click="handleDetail(record)">详情</a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
              <a-button type="link" size="small" @click="handleDelete(record)">删除</a-button>
            </a-button-group>
          </template>
        </c-table>
      </div>
    </div>

    <a-modal
      v-model:visible="visible"
      :title="currentEditIndex === -1 ? '新增记录' : '编辑记录'"
      @ok="handleOk"
      @cancel="handleCancel"
      width="600px"
    >
      <div style="margin-top: 16px">
        <a-form :model="formState" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-form-item label="字段名称">
            <a-input v-model:value="formState.fieldName" placeholder="请输入字段名称" />
          </a-form-item>
          <a-form-item label="字段编号">
            <a-input v-model:value="formState.fieldCode" placeholder="请输入字段编号" />
          </a-form-item>
          <a-form-item label="是否默认勾选">
            <a-select v-model:value="formState.filedRequired" placeholder="请选择">
              <a-select-option value="是">是</a-select-option>
              <a-select-option value="否">否</a-select-option>
            </a-select>
          </a-form-item>
        </a-form>
      </div>
    </a-modal>
    <a-modal
      v-model:visible="detailVisible"
      title="详情"
      @cancel="detailVisible = false"
      :footer="null"
      width="600px"
      class="detail-modal"
    >
      <a-descriptions bordered :column="1">
        <a-descriptions-item label="字段名称">{{ detailState.name }}</a-descriptions-item>
        <a-descriptions-item label="字段编号">{{ detailState.code }}</a-descriptions-item>
        <a-descriptions-item label="是否默认勾选">{{ detailState.required }}</a-descriptions-item>
      </a-descriptions>
      <div class="detail-footer">
        <a-button @click="detailVisible = false">关闭</a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, onMounted, type UnwrapRef } from 'vue';
  import cTable from '/@/views/components/Table/index.vue';
  import {
    getFieldList,
    updateFieldList,
    addFieldList,
    deleteFieldList,
  } from '/@/api/protocolManage/fieldManage';
  import { message } from 'ant-design-vue';
  const loading = ref(false);
  const currentEditIndex = ref(-1);
  const visible = ref(false);
  const labelCol = { span: 6 };
  const wrapperCol = { span: 16 };
  const detailVisible = ref(false);
  const detailState = {
    name: '',
    code: '',
    required: '',
    id: '',
  };
  interface detailState {
    name: string;
    code: string;
    required: string;
    id: string;
  }
  onMounted(() => {
    getList();
  });
  interface FormState {
    fieldName: string;
    fieldCode: string;
    filedRequired: string;
    id: string;
  }

  const formState: UnwrapRef<FormState> = reactive({
    fieldName: '',
    fieldCode: '',
    filedRequired: '否',
    id: '',
  });
  const pagination = reactive({
    currentPage: 1,
    totalItems: 500,
    pageSize: 10,
  });
  // 表格列配置
  const tableColumns = ref([
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center', width: 80 },
    { title: '字段名称', dataIndex: 'name', key: 'name', align: 'center' },
    { title: '字段编号', dataIndex: 'code', key: 'code', align: 'center' },
    {
      title: '是否默认勾选',
      dataIndex: 'required',
      key: 'required',
      align: 'center',
      isSlot: true,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      width: 180,
      isSlot: true,
      slotName: 'action',
    },
  ]);
  const tableData = reactive({
    data: [],
  });
  const reSet = () => {
    searchForm.name = '';
    searchForm.code = '';
    searchForm.required = undefined;
    searchForm.id = '';
    getList();
  };

  const getList = async (flag?: number) => {
    if (!flag) {
      pagination.currentPage = 1;
    }

    loading.value = true;
    tableData.data = [];

    try {
      // 构建查询参数
      const params = {
        page: pagination.currentPage,
        size: pagination.pageSize,
        name: searchForm.name || undefined,
        code: searchForm.code || undefined,
        required: searchForm.required || undefined,
      };

      // 调用API获取数据
      const res = await getFieldList(params);

      // 更新表格数据和分页信息
      if (res && res.list) {
        tableData.data = res.list.map((item, index) => ({
          ...item,
          index: (pagination.currentPage - 1) * pagination.pageSize + index + 1,
          required: item.required === '1',
        }));
        pagination.totalItems = res.total || 0;
      }

      loading.value = false;
    } catch (error) {
      loading.value = false;
    }
  };
  // 事件处理函数
  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    getList(1);
  };
  const searchForm = reactive({
    name: '',
    code: '',
    required: undefined,
    id: '',
  });
  // 确认保存
  const handleOk = async () => {
    // 表单验证
    if (!formState.fieldName || !formState.fieldCode || !formState.filedRequired) {
      message.error('请填写完整信息');
      return;
    }

    try {
      loading.value = true;

      // 准备提交的数据
      const params = {
        name: formState.fieldName,
        code: formState.fieldCode,
        required: formState.filedRequired === '是' ? '1' : '0',
        id: formState.id,
      };

      // 如果是编辑模式，会有ID值
      if (formState.id) {
        // 编辑模式 - 添加ID到参数中
        params.id = formState.id;

        // 调用更新API
        await updateFieldList(params);
        message.success('更新成功');
      } else {
        // 新增模式
        await addFieldList(params);
        message.success('新增成功');
      }

      // 关闭弹窗
      visible.value = false;

      // 重置表单
      resetForm();

      // 刷新列表
      getList();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('操作失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  };
  const handleEdit = (record: any) => {
    console.log('编辑记录:', record);
    currentEditIndex.value = record.id;
    formState.fieldName = record.name;
    formState.fieldCode = record.code;
    formState.filedRequired = record.required ? '是' : '否';
    formState.id = record.id;
    // 打开对话框
    visible.value = true;
  };
  const handleDetail = (record: any) => {
    console.log('查看详情:', record);
    detailState.name = record.name;
    detailState.code = record.code;
    detailState.required = record.required ? '是' : '否';
    detailState.id = record.id;
    detailVisible.value = true;
  };
  // 删除记录
  const handleDelete = (record: any) => {
    console.log('删除记录:', record);
    deleteFieldList([record.id]).then((_) => {
      getList();
      message.success('删除成功');
    });
  };

  // 新增记录
  const handleAdd = () => {
    console.log('新增记录');

    // 重置表单
    resetForm();

    // 设置为新增模式
    currentEditIndex.value = -1;

    // 显示弹窗
    visible.value = true;
  };

  // 取消操作
  const handleCancel = () => {
    visible.value = false;
    resetForm();
  };

  // 重置表单
  const resetForm = () => {
    formState.id = '';
    formState.fieldName = '';
    formState.fieldCode = '';
    formState.filedRequired = '';
  };
</script>

<style scoped lang="less">
  .fieldManage {
    width: 100%;
    height: 100%;
    padding: 8px;
    background-color: white;
    .explore {
      display: flex;
      font-size: 16px;
      font-weight: bold;
      gap: 16px;
      background-color: #fff;
      margin-bottom: 0;
      padding: 16px 0 0 16px;
      align-items: center;
    }

    .label-text {
      white-space: nowrap;
    }

    .button-style {
      > * + * {
        margin-left: 16px;
      }
    }
  }
  .table_box {
    padding: 16px;
  }
  .detail-modal {
    padding: 24px;
    background-color: #fff;
    .detail-footer {
      text-align: right;
      padding: 16px;
    }
  }
</style>
