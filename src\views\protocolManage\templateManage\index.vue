<template>
  <div class="fieldManage">
    <div class="page-container">
      <div class="explore">
        <a-input
          style="width: 240px"
          v-model:value="searchForm.name"
          placeholder="请输入模板名称"
        />
        <a-input
          style="width: 240px"
          v-model:value="searchForm.code"
          placeholder="请输入模板编号"
        />
        <a-input
          style="width: 240px"
          v-model:value="searchForm.uniqueKey"
          placeholder="请输入合同系统唯一标识"
        />
        <a-space direction="vertical" :size="12">
          <a-date-picker
            v-model:value="searchForm.year"
            picker="year"
            placeholder="请选择年份"
            format="YYYY"
            valueFormat="YYYY"
          />
        </a-space>
        <div class="button-style">
          <a-button type="primary" @click="getList()">搜索</a-button>
          <a-button type="primary" @click="handleAdd()">新增</a-button>
          <a-button type="primary" @click="handleEnable()">启用</a-button>
          <a-button type="primary" @click="handleDisable()">禁用</a-button>
        </div>
      </div>

      <div class="table_box">
        <c-table
          :tableColumns="tableColumnsWithCheckbox"
          :tableData="tableData.data"
          :loading="loading"
          :currentPage="pagination.currentPage"
          :totalItems="pagination.totalItems"
          :pageSize="pagination.pageSize"
          :isSelection="false"
          @update:current-page="(value) => (pagination.currentPage = value)"
          @pagination-change="handlePaginationChange"
        >
          <!-- 复选框列 -->
          <template #checkbox="{ record }">
            <a-checkbox
              :checked="selectedRowKeys.includes(record.id)"
              @change="(e) => handleRowSelect(e.target.checked, record)"
            />
          </template>

          <!-- 全选复选框（表头） -->
          <template #checkboxHeader>
            <a-checkbox
              :checked="isAllSelected"
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
            />
          </template>

          <template #type="{ record }">
            <span>{{
              record.type === '1' ? '协议' : record.type === '2' ? '附件' : record.type
            }}</span>
          </template>
          <template #enabledMark="{ record }">
            <a-tag :color="record.enabledMark === 1 ? 'green' : 'red'">
              {{ record.enabledMark === 1 ? '启用' : '禁用' }}
            </a-tag>
          </template>
          <template #action="{ record }">
            <a-button-group>
              <a-button type="link" size="small" @click="handleDetail(record)">详情</a-button>
              <a-button type="link" size="small" @click="handleEdit(record)">编辑</a-button>
              <a-button type="link" size="small" @click="handleDelete(record)">删除</a-button>
            </a-button-group>
          </template>
        </c-table>
      </div>
    </div>

    <a-modal
      v-model:visible="visible"
      :title="modalTitle"
      @ok="handleOk"
      @cancel="handleCancel"
      width="100%"
      wrap-class-name="full-modal"
      :footer="isDetail ? null : undefined"
    >
      <div class="modal-content" :class="{ 'detail-mode': isDetail }">
        <div class="form-section-title">基本信息</div>
        <div class="form-complete">
          <a-form
            :model="formState"
            :label-col="labelCol"
            :wrapper-col="wrapperCol"
            :rules="formRules"
            ref="formRef"
          >
            <div class=".ant-row ant-form-item">
              <a-form-item
                label="模板名称"
                name="fieldName"
                :rules="[{ required: true, message: '请输入模板名称' }]"
              >
                <a-input
                  v-model:value="formState.fieldName"
                  placeholder="请输入模板名称"
                  :disabled="isDetail"
                />
              </a-form-item>
            </div>
            <a-form-item
              label="模板编号"
              name="fieldCode"
              :rules="[{ required: true, message: '请输入模板编号' }]"
            >
              <a-input
                v-model:value="formState.fieldCode"
                placeholder="请输入模板编号"
                :disabled="isDetail"
              />
            </a-form-item>
            <a-form-item
              label="合同系统唯一标识"
              name="fieldUniqueKey"
              :rules="[{ required: true, message: '请输入合同系统唯一标识' }]"
            >
              <a-input
                v-model:value="formState.fieldUniqueKey"
                placeholder="请输入合同系统唯一标识"
                :disabled="isDetail"
              />
            </a-form-item>
            <a-form-item
              label="模板类型"
              name="fieldType"
              :rules="[{ required: true, message: '请输入模板类型' }]"
            >
              <a-select
                v-model:value="formState.fieldType"
                placeholder="请选择模板类型"
                :disabled="isDetail"
              >
                <a-select-option value="1">协议</a-select-option>
                <a-select-option value="2">附件</a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item
              label="协议模板名称"
              name="fieldParentName"
              :rules="[{ required: true, message: '请输入协议模板名称' }]"
            >
              <a-input
                v-model:value="formState.fieldParentName"
                placeholder="请输入协议模板名称"
                :disabled="isDetail"
              />
            </a-form-item>
            <a-form-item
              label="使用年份"
              name="fieldYear"
              :rules="[{ required: true, message: '请输入使用年份' }]"
            >
              <a-select
                v-model:value="formState.fieldYear"
                placeholder="请选择年份"
                :loading="yearLoading"
                :disabled="isDetail"
                show-search
                :filter-option="
                  (input, option) => option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
                "
              >
                <a-select-option
                  v-for="year in yearOptions"
                  :key="year.value"
                  :value="year.value"
                  :label="year.label"
                >
                  {{ year.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item label="模板字段">
              <a-spin :spinning="templateFieldsLoading">
                <div class="template-fields-container">
                  <a-checkbox-group
                    v-model:value="formState.fieldcttTemplateFieldRelList"
                    :disabled="isDetail"
                    style="width: 100%"
                  >
                    <div class="template-fields-grid">
                      <div
                        v-for="field in TemplateFields"
                        :key="field.id"
                        class="template-field-item"
                      >
                        <a-checkbox :value="field.id">
                          {{ field.name }}
                        </a-checkbox>
                      </div>
                    </div>
                  </a-checkbox-group>
                </div>
              </a-spin>
            </a-form-item>
            <!--          <a-form-item label="启用/禁用">-->
            <!--            <a-switch-->
            <!--              v-model:checked="formState.fieldEnabledMark"-->
            <!--              :checked-value="1"-->
            <!--              :unchecked-value="0"-->
            <!--              checked-children="启用"-->
            <!--              unchecked-children="禁用"-->
            <!--            />-->
            <!--          </a-form-item>-->
          </a-form>
        </div>
        <div v-if="isDetail" class="detail-footer">
          <a-button @click="handleCancel">关闭</a-button>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
  import { reactive, ref, onMounted, computed, type UnwrapRef, watch } from 'vue';
  import cTable from '/@/views/components/Table/index.vue';
  import {
    templateList,
    addFieldList,
    deleteFieldList,
    TemplateFieldList,
    enableFieldList,
    disableFieldList,
    updateFieldList,
    yearFieldList,
    infoFieldList,
  } from '/@/api/protocolManage/templateManage';
  import { FormInstance, message } from 'ant-design-vue';
  const loading = ref(false);
  const isDetail = ref(false);
  const currentEditIndex = ref(-1);
  const visible = ref(false);
  const labelCol = { span: 6 };
  const wrapperCol = { span: 16 };
  const TemplateFields = ref<any[]>([]);
  const templateFieldsLoading = ref(false);
  const modalType = ref('add');
  // const rules = {
  //   fieldName: [{ required: true, message: '请输入模板名称' }],
  //   fieldCode: [{ required: true, message: '请输入模板编号' }],
  //   fieldUniqueKey: [{ required: true, message: '请输入合同系统唯一标识' }],
  //   fieldType: [{ required: true, message: '请选择模板类型' }],
  // };
  const formRef = ref<FormInstance>();

  // 简单的表单验证规则 - 只验证必填项
  const formRules = reactive({
    fieldName: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
    fieldCode: [{ required: true, message: '请输入模板编号', trigger: 'blur' }],
    fieldUniqueKey: [{ required: true, message: '请输入合同系统唯一标识', trigger: 'blur' }],
    fieldType: [{ required: true, message: '请选择模板类型', trigger: 'change' }],
    fieldParentName: [{ required: true, message: '请输入协议模板名称', trigger: 'blur' }],
    fieldYear: [{ required: true, message: '请选择使用年份', trigger: 'change' }],
  });

  const modalTitle = computed(() => {
    if (modalType.value === 'add') return '新增';
    if (modalType.value === 'edit') return '编辑';
    return '详情';
  });

  onMounted(() => {
    getList();
    getYearList();
  });
  const yearOptions = ref<any[]>([]); // 年份选项列表
  const yearLoading = ref(false); // 年份加载状态

  // 2. 添加获取年份列表的方法（请根据实际API调整）
  const getYearList = async () => {
    yearLoading.value = true;
    try {
      // 替换为你的实际年份接口
      const res = await yearFieldList();

      if (res && Array.isArray(res)) {
        yearOptions.value = res.map((item) => ({
          value: item.id || item.value || item.year, // 根据实际字段调整
          label: item.name || item.label || item.year, // 根据实际字段调整
        }));
      } else if (res && res.list) {
        // 如果返回格式是 {list: [...]}
        yearOptions.value = res.list.map((item) => ({
          value: item.id || item.value || item.year,
          label: item.name || item.label || item.year,
        }));
      }

      console.log('获取到的年份列表:', yearOptions.value);
    } catch (error) {
      console.error('获取年份列表失败:', error);
      message.error('获取年份列表失败，请稍后重试');
      yearOptions.value = [];
    } finally {
      yearLoading.value = false;
    }
  };

  // 选择相关的响应式数据
  const selectedRowKeys = ref<string[]>([]); // 选中的行ID数组
  const selectedRows = ref<any[]>([]); // 选中的完整行数据数组

  // 处理单行选择
  const handleRowSelect = (checked: boolean, record: any) => {
    console.log('行选择状态改变:', checked, record);

    if (checked) {
      // 勾选：添加到选中列表
      if (!selectedRowKeys.value.includes(record.id)) {
        selectedRowKeys.value.push(record.id);
        selectedRows.value.push(record);
      }
    } else {
      // 取消勾选：从选中列表移除
      const index = selectedRowKeys.value.indexOf(record.id);
      if (index > -1) {
        selectedRowKeys.value.splice(index, 1);
        selectedRows.value.splice(index, 1);
      }
    }

    console.log('当前选中的IDs:', selectedRowKeys.value);
    console.log('当前选中的行数据:', selectedRows.value);
  };

  const handleSelectAll = (e: any) => {
    const checked = e.target.checked;
    console.log('全选状态改变:', checked);

    if (checked) {
      // 全选：选中当前页所有数据
      selectedRowKeys.value = tableData.data.map((item) => item.id);
      selectedRows.value = [...tableData.data];
    } else {
      // 取消全选：清空选择
      selectedRowKeys.value = [];
      selectedRows.value = [];
    }

    console.log('全选后的选中IDs:', selectedRowKeys.value);
  };

  // 清空选择
  const clearSelection = () => {
    selectedRowKeys.value = [];
    selectedRows.value = [];
    console.log('已清空所有选择');
  };

  // 计算全选状态
  const isAllSelected = computed(() => {
    return tableData.data.length > 0 && selectedRowKeys.value.length === tableData.data.length;
  });

  // 计算半选状态（部分选中）
  const isIndeterminate = computed(() => {
    return selectedRowKeys.value.length > 0 && selectedRowKeys.value.length < tableData.data.length;
  });

  interface FormState {
    fieldName: string;
    fieldCode: string;
    fieldUniqueKey: string;
    fieldYear: string;
    fieldSubType: string;
    fieldParentName: string;
    id: string;
    fieldEnabledMark: number;
    fieldType: string | undefined;
    fieldcttTemplateFieldRelList: [];
  }

  const formState: UnwrapRef<FormState> = reactive({
    fieldName: '',
    fieldCode: '',
    fieldUniqueKey: '',
    fieldYear: '',
    fieldSubType: '',
    fieldParentName: '',
    id: '',
    fieldEnabledMark: 1,
    fieldType: undefined,
    fieldcttTemplateFieldRelList: [],
  });

  const pagination = reactive({
    currentPage: 1,
    totalItems: 500,
    pageSize: 10,
  });

  // 原始表格列配置
  const originalTableColumns = ref([
    { title: '序号', dataIndex: 'index', key: 'index', align: 'center', width: 80 },
    { title: '模板名称', dataIndex: 'name', key: 'name', align: 'center' },
    { title: '模板编号', dataIndex: 'code', key: 'code', align: 'center' },
    { title: '合同系统唯一标识', dataIndex: 'uniqueKey', key: 'uniqueKey', align: 'center' },
    { title: '使用年份', dataIndex: 'year', key: 'year', align: 'center' },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      align: 'center',
      isSlot: true,
      slotName: 'type',
    },
    { title: '协议模板名称', dataIndex: 'parentName', key: 'parentName', align: 'center' },
    {
      title: '启用/禁用',
      dataIndex: 'enabledMark',
      key: 'enabledMark',
      align: 'center',
      isSlot: true,
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      width: 180,
      isSlot: true,
      slotName: 'action',
    },
  ]);

  // 添加复选框列的表格配置
  const tableColumnsWithCheckbox = computed(() => {
    return [
      // 添加复选框列
      {
        title: '', // 表头使用插槽显示全选复选框
        dataIndex: 'checkbox',
        key: 'checkbox',
        align: 'center',
        width: 50,
        isSlot: true,
        slotName: 'checkbox',
        headerSlotName: 'checkboxHeader',
      },
      // 原有的列
      ...originalTableColumns.value,
    ];
  });

  const tableData = reactive({
    data: [],
  });

  const searchForm = reactive({
    name: '',
    code: '',
    uniqueKey: '',
    year: '',
    subType: '',
    parentName: '',
    type: '',
    enabledMark: '',
    cttTemplateFieldRelList: [],
  });

  const getList = async (flag?: number) => {
    if (!flag) {
      pagination.currentPage = 1;
      // 切换页面时清空选择
      clearSelection();
    }

    loading.value = true;
    tableData.data = [];

    try {
      const params = {
        page: pagination.currentPage,
        size: pagination.pageSize,
        name: searchForm.name || undefined,
        code: searchForm.code || undefined,
        uniqueKey: searchForm.uniqueKey || undefined,
        year: searchForm.year || undefined,
        parentName: searchForm.parentName || undefined,
        type: searchForm.type || undefined,
        enabledMark: searchForm.enabledMark || undefined,
        cttTemplateFieldRelList: searchForm.cttTemplateFieldRelList,
      };

      const res = await templateList(params);

      if (res && res.list) {
        tableData.data = res.list.map((item, index) => ({
          ...item,
          index: (pagination.currentPage - 1) * pagination.pageSize + index + 1,
          required: item.required === '1',
        }));
        pagination.totalItems = res.total || 0;
      }

      loading.value = false;
    } catch (error) {
      loading.value = false;
    }
  };

  const handlePaginationChange = (page: any) => {
    pagination.currentPage = page.current;
    pagination.pageSize = page.pageSize;
    getList(1);
  };

  const handleOk = async () => {
    // if (
    //   !formState.fieldName ||
    //   !formState.fieldCode ||
    //   !formState.fieldUniqueKey ||
    //   !formState.fieldYear ||
    //   !formState.fieldParentName ||
    //   !formState.fieldType
    // ) {
    //   message.error('请填写完整信息');
    //   return;
    // }
    if (isDetail.value) {
      visible.value = false;
      return;
    }

    try {
      loading.value = true;
      console.log(formState.fieldcttTemplateFieldRelList);
      const params = {
        name: formState.fieldName,
        code: formState.fieldCode,
        uniqueKey: formState.fieldUniqueKey,
        year: formState.fieldYear,
        subType: formState.fieldSubType,
        parentName: formState.fieldParentName,
        id: formState.id,
        enabledMark: formState.fieldEnabledMark,
        type: formState.fieldType,
        cttTemplateFieldRelList: formState.fieldcttTemplateFieldRelList.map((item: any) =>
          TemplateFields.value.find((field: any) => field.id === item),
        ),
      };

      if (formState.id) {
        params.id = formState.id;
        await updateFieldList(params);
        message.success('编辑成功');
      } else {
        await addFieldList(params);
        message.success('新增成功');
      }

      visible.value = false;
      resetForm();
      getList();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('操作失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  };

  const getTemplateFieldList = async () => {
    templateFieldsLoading.value = true;
    try {
      const params = {
        page: 1,
        size: 100, // 获取足够多的字段
      };

      const res = await TemplateFieldList(params);
      if (res) {
        TemplateFields.value = res.map((item) => ({
          id: item.id,
          name: item.name,
          code: item.code,
          required: item.required,
        }));
      } else {
        TemplateFields.value = [];
      }

      console.log('获取到的模板字段列表:', TemplateFields.value);
    } catch (error) {
      console.error('获取模板字段失败:', error);
      message.error('获取模板字段失败，请稍后重试');
      TemplateFields.value = [];
    } finally {
      templateFieldsLoading.value = false;
    }
  };

  // 在visible变化时获取模板字段
  watch(
    () => visible.value,
    (newVal) => {
      if (newVal) {
        getTemplateFieldList();
      }
    },
  );

  const handleEdit = (record: any) => {
    modalType.value = 'edit';
    console.log('编辑记录:', record);
    currentEditIndex.value = record.id;
    formState.fieldName = record.name;
    formState.fieldCode = record.code;
    formState.fieldUniqueKey = record.uniqueKey;
    formState.fieldYear = record.year;
    formState.fieldSubType = record.subType;
    formState.fieldParentName = record.parentName;
    formState.fieldEnabledMark = record.enabledMark;
    formState.fieldType = record.type;
    formState.id = record.id;

    // 修复：确保字段列表正确回显
    if (record.cttTemplateFieldRelList && Array.isArray(record.cttTemplateFieldRelList)) {
      // 如果后端返回的是完整对象数组，提取ID
      formState.fieldcttTemplateFieldRelList = record.cttTemplateFieldRelList.map((item: any) => {
        return typeof item === 'object' ? item.id : item;
      });
    } else if (
      record.cttTemplateFieldRelList &&
      typeof record.cttTemplateFieldRelList === 'string'
    ) {
      // 如果是字符串，尝试解析
      try {
        const parsed = JSON.parse(record.cttTemplateFieldRelList);
        formState.fieldcttTemplateFieldRelList = Array.isArray(parsed)
          ? parsed.map((item: any) => (typeof item === 'object' ? item.id : item))
          : [];
      } catch (e) {
        console.error('解析字段列表失败:', e);
        formState.fieldcttTemplateFieldRelList = [];
      }
    } else {
      formState.fieldcttTemplateFieldRelList = [];
    }

    console.log('编辑时的字段列表:', formState.fieldcttTemplateFieldRelList);
    visible.value = true;
    isDetail.value = false;
  };

  const handleDelete = (record: any) => {
    console.log('删除记录:', record);
    deleteFieldList([record.id]).then((_) => {
      getList();
      message.success('删除成功');
    });
  };

  const handleEnable = async () => {
    if (selectedRowKeys.value.length === 0) {
      message.warning('请先选择要启用的记录');
      return;
    }
    try {
      loading.value = true;
      await enableFieldList(selectedRowKeys.value);
      message.success('启用成功');
      getList(); // 刷新列表
      clearSelection(); // 清空选择
    } catch (error) {
      console.error('启用失败:', error);
      message.error('启用失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  };

  const handleDisable = async () => {
    if (selectedRowKeys.value.length === 0) {
      message.warning('请先选择要禁用的记录');
      return;
    }
    try {
      loading.value = true;
      await disableFieldList(selectedRowKeys.value);
      message.success('禁用成功');
      getList(); // 刷新列表
      clearSelection(); // 清空选择
    } catch (error) {
      console.error('禁用失败:', error);
      message.error('禁用失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  };

  const handleAdd = () => {
    modalType.value = 'add';
    console.log('新增记录');
    resetForm();
    currentEditIndex.value = -1;
    visible.value = true;
    isDetail.value = false;
  };

  const handleCancel = () => {
    visible.value = false;
    resetForm();
  };

  const handleDetail = async (record: any) => {
    modalType.value = 'detail';
    console.log('查看详情:', record);
    try {
      loading.value = true;

      // 调用获取详情的API
      const detailRes = await infoFieldList({ id: record.id });

      if (detailRes) {
        formState.fieldName = detailRes.name;
        formState.fieldCode = detailRes.code;
        formState.fieldUniqueKey = detailRes.uniqueKey;
        formState.fieldYear = detailRes.year;
        formState.fieldSubType = detailRes.subType;
        formState.fieldParentName = detailRes.parentName;
        formState.fieldEnabledMark = detailRes.enabledMark;
        formState.fieldType = detailRes.type;
        formState.id = detailRes.id;

        if (detailRes.cttTemplateFieldRelList && Array.isArray(detailRes.cttTemplateFieldRelList)) {
          // 如果是对象数组，提取name属性
          formState.fieldcttTemplateFieldRelList = detailRes.cttTemplateFieldRelList
            .map((item) => {
              // 如果item是对象且有name属性，返回name
              if (typeof item === 'object' && item !== null) {
                return item.id;
              }
              // 如果item是字符串，直接返回
              return item;
            })
            .filter((id) => id); // 过滤掉空值
        } else {
          formState.fieldcttTemplateFieldRelList = [];
        }

        visible.value = true;
        isDetail.value = true;
      } else {
        message.error('获取详情失败');
      }
    } catch (error) {
      console.error('获取详情失败:', error);
      message.error('获取详情失败，请稍后重试');
    } finally {
      loading.value = false;
    }
  };

  const resetForm = () => {
    formState.id = '';
    formState.fieldName = '';
    formState.fieldCode = '';
    formState.fieldUniqueKey = '';
    formState.fieldYear = '';
    formState.fieldSubType = '';
    formState.fieldParentName = '';
    formState.fieldEnabledMark = 1;
    formState.fieldType = undefined;
    formState.fieldcttTemplateFieldRelList = [];
  };
</script>

<style scoped lang="less">
  .fieldManage {
    width: 100%;
    height: 100%;
    padding: 8px;
    background-color: white;

    .explore {
      display: flex;
      font-size: 16px;
      font-weight: bold;
      gap: 16px;
      background-color: #fff;
      margin-bottom: 0;
      padding: 16px 0 0 16px;
      align-items: center;
    }

    .label-text {
      white-space: nowrap;
    }

    .button-style {
      > * + * {
        margin-left: 16px;
      }
    }
    .selection-info {
      padding: 8px 16px;
      background-color: #f0f7ff;
      border: 1px solid #d9f2ff;
      border-radius: 4px;
      margin: 8px 16px;
      display: flex;
      align-items: center;
      justify-content: space-between;

      span {
        color: #1890ff;
        font-weight: 500;
      }
    }
  }
  .form-section-title {
    font-size: 18px;
    font-weight: bold;
    margin-block-start: 0.83em;
    margin-block-end: 0.83em;
    margin-inline-start: 0px;
    margin-inline-end: 0px;
    margin-top: 0;
    margin-bottom: 9px;
    text-align: center;
  }

  .table_box {
    padding: 16px;
  }
</style>
<style lang="less">
  .full-modal {
    .ant-modal-title {
      position: relative;
      display: flex;
      padding-left: 8px;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: #000000d9;
      cursor: pointer;
    }
    .ant-row {
      display: flex;
      flex-flow: row wrap;
    }
    .ant-form-item {
      box-sizing: border-box;
      margin: 0 0 24px;
      padding: 0;
      color: #000000d9;
      font-size: 14px;
      font-variant: tabular-nums;
      line-height: 1.5715;
      list-style: none;
      vertical-align: top;
    }
    .form-complete {
      overflow: auto;
    }
    .modal-content {
      width: 100%;
      height: 100%;
      padding: 14px;
      .detail-footer {
        text-align: right;
        padding: 16px;
      }
      color: #000000;
    }
    .ant-modal {
      max-width: 100%;
      top: 0;
      padding-bottom: 0;
      margin: 0;
    }
    .ant-modal-content {
      display: flex;
      flex-direction: column;
      height: calc(100vh);
    }
    .ant-modal-body {
      flex: 1;
    }
    .template-fields-container {
      border: 1px solid #f0f0f0;
      padding: 8px;
      border-radius: 4px;
    }

    .template-fields-grid {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
    }

    .template-field-item {
      flex: 0 0 auto;
      margin-right: 10px;
      margin-bottom: 10px;
      min-width: 120px; /* 设置最小宽度 */
    }
  }
</style>
